import { useState } from "react";
import axios from 'axios';
import {getConnectionUrl} from "../../websockets/wsClient";

export function useGetObjectPoster() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const getObjectPoster = async (objectId: number): Promise<Blob> => {
        setIsLoading(true);
        setError(null);

        try {
            const response = await axios.get(
                `${getConnectionUrl()}/api/object_poster/${objectId}`,
                {
                    responseType: 'blob'
                }
            );
            return response.data;
        } catch (err) {
            const errorObj = err as Error;
            setError(errorObj);
            throw errorObj;
        } finally {
            setIsLoading(false);
        }
    };

    return { getObjectPoster, isLoading, error };
}

export default useGetObjectPoster;