import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetState() {
  const [isLoadingState, setIsLoading] = useState(false);
  const [errorState, setError] = useState<Error | null>(null);

  const sendGetState = async () => {
    setIsLoading(true);
    setError(null);

    console.log("sendGetState called");

    try {
      sendMessage(
          wsMessages.getState,{}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendGetState, isLoadingState, errorState };
}
