# Traffique Content Studio Frontend

A sophisticated web application for video content management, editing, and product tagging. This application allows users to track objects in videos, tag products, and manage content through integration with Umbraco CMS.

## Features

- **Video Editing**: Frame-by-frame video navigation and editing
- **Object Tracking**: Track objects across video frames with polygon visualization
- **Product Tagging**: Tag products in videos with links, names, and brands
- **Content Management**: Integrate with Umbraco CMS for content management
- **Real-time Processing**: WebSocket communication for real-time tracking and editing
- **Media Management**: Organize and manage photos, videos, models, products, and brands
- **Data Visualization**: Charts and word clouds for data visualization
- **Responsive Layouts**: Various grid and masonry layouts for content display

## Technologies Used

- **Frontend**: React, TypeScript, Material UI
- **Video Processing**: Video.js, FFMPEG
- **State Management**: React Query
- **Styling**: SASS, Emotion
- **API Communication**: Axios, WebSockets
- **Routing**: React Router
- **Data Visualization**: Recharts
- **CMS Integration**: Umbraco CMS
- **Cloud Services**: Azure Functions

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Access to Umbraco CMS instance
- Access to Azure Function App (for serverless functions)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/traffique-content-studio-frontend.git
   cd traffique-content-studio-frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   REACT_APP_HOST_ADRESS=your_host_address
   UMBRACO_ADDRESS=your_umbraco_address
   VIDEO_TEMPLATE_ID=your_video_template_id
   VIDEO_DOCUMENT_TYPE_ID=your_video_document_type_id
   VIDEO_PARENT_ID=your_video_parent_id
   ```

## Running the Application

### Development Mode

```bash
npm start
# or
yarn start
```

This runs the app in development mode. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Production Build

```bash
npm run build
# or
yarn build
```

This builds the app for production to the `build` folder.

## Usage

### Video Editing

1. Navigate to the Clique Videos page (`/clique`)
2. Select a video to edit or upload a new one
3. Use the video editor to track objects, add products, and annotate frames
4. Save your changes and publish when ready

### Content Management

1. Add products, brands, and models through their respective pages
2. Tag these items in videos for interactive content
3. Publish content to make it available on the frontend

## API Documentation

The application communicates with several backend services:

- **Umbraco CMS API**: For content management
- **WebSocket API**: For real-time video processing and tracking
- **Azure Functions**: For serverless processing tasks

For detailed API documentation, please refer to the internal documentation or contact the development team.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Support

For support, please contact the development team or raise an issue in the repository.