@import '../../styles/_variables.scss';

.controlPanel {
    min-width: 300px;
    .topArea {
        .infoElement {
            margin-bottom: 20px;
            .header {
                background-color: $softWhite;
                p {
                    font-size: 20px;
                    font-weight: 600;
                    text-align: center;
                    color: $mediumGray;
                    padding: 10px;
                    letter-spacing: 1px;
                }
            }
            .infoData {
                input {
                    width: 100%;
                    margin-top: 20px;
                    padding: 10px;
                    border: 1px solid $lightGrayBlue;
                    text-align: center;
                    font-size: 20px;
                    font-weight: 500;
                }
            }
        }
    }
    .mlMachineStatusContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .mlMachineStatus {
            display: flex;
            align-items: center;

            div {
                width: 15px;
                height: 15px;
                border-radius: 50%;
                margin-right: 10px;
            }

            .redDot {
                background-color: $red;
            }

            .greenDot {
                background-color: $green;
            }
        }
    }
    .buttonContainer {
        margin-top: 20px;
        button {
            &:nth-child(2) {
                margin: 0 15px;
            }
        }
    }
}