import { useParams } from "react-router-dom";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from "./photographersPage.module.scss";
import PhotographerComponent from "../../components/photographerComponent/PhotographerComponent";
import { useGetUmbracoContentQuery } from "../../api/useGetUmbracoContentQuery";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";

const PhotographersPage = () => {
    const { id } = useParams();
    const { data: item, isLoading, error } = useGetUmbracoContentQuery(id!);

    if (isLoading) return <Loader />;
    if (error || !item) return <MessageAlert type="error" message="Error loading photographer data" />;

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <PhotographerComponent item={item} />
            </div>
        </div>
    );
};

export default PhotographersPage;
