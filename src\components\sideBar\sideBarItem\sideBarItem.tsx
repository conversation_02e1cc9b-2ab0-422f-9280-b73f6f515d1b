import { Card, CardContent, Typography } from "@mui/material";
import styles from "../sidebar.module.scss";

interface SidebarItemProps {
  title: string;
  count?: string | null;
  isActive: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  title,
  count,
  isActive,
}) => (
  <Card className={isActive ? styles.sidebarItemActive : styles.sidebarItem}>
    <CardContent sx={{ padding: 0, "&:last-child": { paddingBottom: 0 } }}>
      <Typography variant="h6">{title}</Typography>
      {count !== undefined && (
        <Typography variant="body2" color="textSecondary">
          {count}
        </Typography>
      )}
    </CardContent>
  </Card>
);

export default SidebarItem;
