import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetVideoInfo() {
  const [isLoadingGetVideoInfo, setIsLoading] = useState(false);
  const [errorVideoInfo, setError] = useState<Error | null>(null);

  const sendGetVideoInfo = async () => {
    setIsLoading(true);
    setError(null);

    console.log("sendGetVideoInfo called");

    try {
      sendMessage(
          wsMessages.getVideoInfo,{}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendGetVideoInfo, isLoadingGetVideoInfo, errorVideoInfo };
}
