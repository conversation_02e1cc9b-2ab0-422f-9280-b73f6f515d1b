import {<PERSON>, Card, CardContent, CircularProgress, Grid, Typography} from "@mui/material";
import VideoJS from "../videoJS/videoJS";
import {useMemo} from "react";
import {UMBRACO_ADRESS} from "../../constants/urls";
import {ISelectionPoint} from "../../types/selectionPoint";
import {SessionState} from "../../enums/sessionState";

interface VideoPlayerContainerProps {
    loading: boolean;
    sessionState: SessionState;
    sessionStateRef: React.MutableRefObject<SessionState>;
    selectionPointsRef: React.MutableRefObject<Record<number, ISelectionPoint[]>>
    currentFrame: number;
    currentFrameRef: React.MutableRefObject<number>;
    boxRef: React.RefObject<HTMLDivElement>;
    videoCanvasRef: React.RefObject<HTMLCanvasElement>;
    svgRef: React.RefObject<SVGSVGElement>;
    videoRef: React.RefObject<any>;
    playerRef: React.RefObject<any>;
    videoUrl: string;
    isPlayingRef: React.MutableRefObject<boolean>;
    handleVideoClick: (e: React.MouseEvent<HTMLCanvasElement>, label: number) => Promise<void>;
    handleReady: (player: any) => void;
    handleProgress: (state: { playedSeconds: number }) => void;
    hasTrackingResult: (frame: number) => boolean;
    setWaitingForFrame: (frame: number) => void;
    setHoveredMarker: (markerId: string | null) => void;
    hoveredMarker: string | null;
    label: boolean;
    sendRemovePoint: (point: ISelectionPoint) => void;
}

export const VideoPlayerContainer: React.FC<VideoPlayerContainerProps> = ({
    loading,
    sessionState,
    sessionStateRef,
    selectionPointsRef,
    currentFrame,
    currentFrameRef,
    boxRef,
    videoCanvasRef,
    svgRef,
    videoRef,
    playerRef,
    videoUrl,
    isPlayingRef,
    handleVideoClick,
    handleReady,
    handleProgress,
    hasTrackingResult,
    setWaitingForFrame,
    label,
    sendRemovePoint,
}) => {

    const handleRemovePoint = (point: ISelectionPoint) => {
        sendRemovePoint(point);
    }

    const memoizedVideoJS = useMemo(
        () => (
            <VideoJS
                videoRef={videoRef}
                playerRef={playerRef}
                url={`${UMBRACO_ADRESS}/${videoUrl!}`}
                playing={isPlayingRef.current}
                controls={true}
                autoPlay={false}
                fluid={true}
                onReady={handleReady}
                onProgress={handleProgress}
                hasTrackingResult={hasTrackingResult}
                setWaitingForFrame={setWaitingForFrame}
                currentFrameRef={currentFrameRef}
                sessionState={sessionState}
            />
        ),
        [videoUrl, isPlayingRef, sessionState]
    );

    return (
        <Grid
            sx={{
                display: "flex",
                justifyContent: "center",
                width: "100%",
            }}
        >
            <Card
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: 5,
                    width: "100%",
                    bgcolor: "#F5F5F5",
                    paddingBottom: "1.5rem"
                }}
            >
                <CardContent
                    sx={{
                        boxSizing: "border-box",
                        maxWidth: "1200px",
                        width: "100%",
                    }}
                >
                    <Box
                        ref={boxRef}
                        sx={{
                            position: "relative",
                            margin: "0 auto",
                        }}
                    >
                        {loading && (
                            <Typography
                                align="center"
                                color="#4D4D4D"
                                sx={{
                                    display: "flex",
                                    height: "100%",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <CircularProgress
                                    sx={{ color: "#4D4D4D", mr: 1.5 }}
                                    size={26}
                                />{" "}
                                Loading...
                            </Typography>
                        )}
                        {!loading  && <canvas
                            ref={videoCanvasRef}
                            style={{
                                position: "absolute",
                                width: "100%",
                                height: "100%",
                                zIndex: "999",
                                top: 0,
                                left: 0,
                                //border: "1px solid rgba(255, 255, 255, 0.2)",
                                //borderRadius: "10px",
                                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            }}
                            onClick={
                                true
                                    ? (e) => handleVideoClick(e, label ? 1 : 0)
                                    : undefined
                            }
                        />}
                        {!loading && memoizedVideoJS}
                        <svg
                            ref={svgRef}
                            width="100%"
                            height="100%"
                            style={{
                                position: "absolute",
                                zIndex: "1000",
                                top: 0,
                                left: 0,
                                pointerEvents: "none",
                            }}
                        >
                            {selectionPointsRef.current[currentFrame]?.map((point, pointIndex) =>
                                {
                                        const {x, y} = point.coordinates;
                                        const label = point.label;
                                        const markerId = `${point.objectId}-${point.frameIndex}-${pointIndex}`;

                                        return (
                                            <g
                                                key={markerId}
                                                style={{ pointerEvents: "auto", cursor: "pointer" }}
                                                onClick={() => handleRemovePoint(point)}
                                                onMouseEnter={(e) => {
                                                    e.currentTarget.querySelector('circle')?.setAttribute('r', '14')
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.currentTarget.querySelector('circle')?.setAttribute('r', '10')
                                                }}
                                            >
                                                <circle
                                                    cx={`${x * 100}%`}
                                                    cy={`${y * 100}%`}
                                                    r='10'
                                                    fill={label === 1 ? "black" : "red"}
                                                    stroke="white"
                                                />
                                                {label === 1 ?
                                                    <text
                                                        x={`${x * 100}%`}
                                                        y={`${y * 100}%`}
                                                        dy="0.35em"
                                                        textAnchor="middle"
                                                        fill="white"
                                                        fontSize="15"
                                                    >
                                                        +
                                                    </text>
                                                :
                                                    <text
                                                        x={`${x * 100}%`}
                                                        y={`${y * 100}%`}
                                                        dy="0.35em"
                                                        textAnchor="middle"
                                                        fill="white"
                                                        fontSize="15"
                                                    >
                                                        -
                                                    </text>
                                                }
                                            </g>
                                        );
                                })
                            }
                        </svg>
                    </Box>
                </CardContent>
            </Card>
        </Grid>
    );
}