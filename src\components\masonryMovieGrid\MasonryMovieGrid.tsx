import { Link } from "react-router-dom";
import styles from "./masonryMovieGrid.module.scss";
import MessageAlert from "../messageAlert/MessageAlert";
import Loader from "../loader/Loader";
import useGetShopPageVideoData from "../../api/useGetShopPageVideoData";
import { UMBRACO_ADRESS } from "../../constants/urls";

interface MasonryMovieGridProps {
    searchQuery: string;
    selectedCity: string;
}

const MasonryMovieGrid: React.FC<MasonryMovieGridProps> = ({ searchQuery, selectedCity }) => {
    const { data, isLoading, error } = useGetShopPageVideoData();
    console.log(data)

    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading data" />;
    if (!data || data.length === 0) return <MessageAlert type="error" message="No data available" />;

    const filteredData = data.filter((shopPage: any) => shopPage.properties.coverPhoto?.[0]?.url);

    const cityFilteredData = selectedCity === 'Everywhere'
        ? filteredData
        : filteredData.filter((shopPage: any) => shopPage.properties.location === selectedCity);

    const searchFilteredData = filteredData.filter((shopPage: any) => {
        const searchFields = Object.values(shopPage.properties || {}).map(value =>
            typeof value === 'string' ? value.toLowerCase() : ''
        );
        console.log(searchFields)
        return searchQuery === '' || searchFields.some(field => field.startsWith(searchQuery.toLowerCase()));
    });

    return (
        <>
            {isLoading && <Loader />}
            {(!data || data.length === 0) && <MessageAlert type="error" message="No data available" />}
            {error && <MessageAlert type="error" message="Error loading data" />}
            <div className={styles.masonryGrid}>
                {searchFilteredData.map((item: any, index: number) => {
                    const imgSrc = item.properties.coverPhoto?.[0]?.url
                        ? `${UMBRACO_ADRESS}${item.properties.coverPhoto[0].url}`
                        : '';
                    const title = item.name || 'Untitled';
                    return (
                        <Link to={'/clique-video-editor/' + item.id} key={item.id}>
                            <div key={index} className={styles.gridItem}>
                                <img src={imgSrc} alt="media" />
                                <div className={styles.textBlock}>
                                    <h1>{title}</h1>
                                    <p>VIEWS 140</p>
                                </div>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </>
    );
};

export default MasonryMovieGrid;