@import '../../styles/_variables.scss';

.photosPage {
    padding: 20px;
    background-color: $white;
}

.contentWrapper {
    background: $white;
    max-width: 1510px;
    margin: 100px auto 0 auto;
}

.month {
    font-weight: 600;
    font-size: 28px;
    gap: 20px;
    background-color: $white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 40px;
}

.faves {
    font-weight: 600;
    font-size: 18px;
    gap: 20px;
    background-color: $white;
    display: flex;
    align-items: center;
    padding-bottom: 40px;
}

.container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 10px;
    padding-top: 40px;
}