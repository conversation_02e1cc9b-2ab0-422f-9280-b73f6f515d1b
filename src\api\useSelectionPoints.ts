import { useState, useRef } from "react";
import { ISelectionPoint } from "../types/selectionPoint"
import {ObjectResult} from "../types/polygons.type";
import {Simulate} from "react-dom/test-utils";
import select = Simulate.select;

export const useSelectionPoints = () => {
    const [selectionPoints, setSelectionPoints] = useState<Record<number, ISelectionPoint[]>>({});
    const selectionPointsRef = useRef<Record<number, ISelectionPoint[]>>({});

    const addSelectionPoint = (point: ISelectionPoint) => {
        const frameIdx = point.frameIndex;
        setSelectionPoints(prev => {
            const updated = {
                ...prev,
                [frameIdx]: [...(prev[frameIdx] || []), point]
            };
            selectionPointsRef.current = updated;
            return updated;
        });
    };

    const removeSelectionPoint = (target: ISelectionPoint) => {
        const frameIdx = target.frameIndex;
        setSelectionPoints(prev => {
            const points = prev[frameIdx] || [];
            const updated = {
                ...prev,
                [frameIdx]: points.filter(p =>
                    !(
                        p.frameIndex === target.frameIndex &&
                        p.objectId === target.objectId &&
                        p.label === target.label &&
                        p.coordinates.x === target.coordinates.x &&
                        p.coordinates.y === target.coordinates.y
                    )
                )
            };
            selectionPointsRef.current = updated;
            return updated;
        });
    };

    const removeSelectionPointsForObject = (objectId: number) => {
        console.log("DELETE SELECTION POINTS FOR OBJECT", objectId);

        const updatedResults = { ...selectionPoints };

        // Iterate through each frame and filter out selection points with matching objectId
        Object.keys(updatedResults).forEach(frameIdx => {
            const numFrameIdx = Number(frameIdx);
            updatedResults[numFrameIdx] = updatedResults[numFrameIdx].filter(
                (point: ISelectionPoint) => point.objectId !== objectId
            );
        });

        console.log("UPDATED TRACKING RESULTS", updatedResults);

        selectionPointsRef.current = updatedResults;
        setSelectionPoints(updatedResults);
    }

    const replaceSelectionPoints = (newPoints: Record<number, ISelectionPoint[]>) => {
        selectionPointsRef.current = newPoints;
        setSelectionPoints(newPoints);
    };

    return { selectionPoints, selectionPointsRef, setSelectionPoints, addSelectionPoint, removeSelectionPoint, removeSelectionPointsForObject, replaceSelectionPoints };
};
