import styles from './photographerComponent.module.scss'
import {
    Box,
    Grid,
    TextField,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Button,
    Paper,
    TextareaAutosize,
} from "@mui/material";
import { JSXElementConstructor, Key, ReactElement, ReactNode, ReactPortal, useState } from 'react';
import useUpdatePhotographerItem from '../../api/useUpdatePhotographerItem';
import useDeleteDocument from '../../api/useDeleteDocument';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from "@tanstack/react-query";
import DeleteIcon from '@mui/icons-material/Delete';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DoneIcon from '@mui/icons-material/Done';
import DeleteConfirmationModal from '../deleteConfirmationModal/DeleteConfirmationModal';
import { CLIENTAPP_FRONTEND_ADDRESS, UMBRACO_ADRESS } from "../../constants/urls";
import SaveConfirmationModal from '../saveConfirmationModal/SaveConfirmationModal';

const PhotographerComponent = ({ item }: any) => {
    const {
        properties: { description, image, photographerName, websiteLink, cities },
    } = item;

    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
    const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
    const [newCity, setNewCity] = useState("");

    const [cityList, setCityList] = useState(
        cities.items.map((city: any) => city.content.properties.cityname)
    );

    const [updatedFields, setUpdatedFields] = useState({
        photographerName,
        description,
        websiteLink,
    });

    const { updatePhotographerItem } = useUpdatePhotographerItem();
    const { deleteDocument } = useDeleteDocument();

    const navigate = useNavigate();
    const queryClient = useQueryClient();

    const handleDelete = async () => {
        try {
            await deleteDocument(item.id);
            queryClient.invalidateQueries({ queryKey: ["photographerInfo"] });
            navigate("/photographers");
        } catch (error) {
            console.error("Error deleting photographer:", error);
        }
    };

    const handleSave = async () => {
        if (!item.id) return;

        const updatedData = {
            title: updatedFields.photographerName,
            description: updatedFields.description,
            photographer: updatedFields.websiteLink,
            cities: cityList,
            image: item.properties.image,
        };

        try {
            await updatePhotographerItem(item.id, updatedData);
            setIsSaveModalOpen(true);
            queryClient.invalidateQueries({ queryKey: ["photographerInfo"] });
        } catch (error) {
            console.error("Error updating photographer item:", error);
        }
    };

    const handleChangeField = (field: string, value: string) => {
        setUpdatedFields((prev) => ({ ...prev, [field]: value }));
    };

    const handleAddCity = () => {
        if (!newCity.trim()) return;

        setCityList((prevCities: any) => [...prevCities, newCity]);
        setNewCity("");
    };

    return (
        <>
            <div className={styles.fashionComponent}>
                <div className={styles.header}>
                    <div>
                        <div className={`${styles.button}`} onClick={() => setIsDeleteModalOpen(true)}>
                            <DeleteIcon /> <p>Delete</p>
                        </div>
                        <div className={`${styles.button}`} onClick={() => window.open(CLIENTAPP_FRONTEND_ADDRESS + "photographer" + item.route.path, "_blank")}>
                            <RemoveRedEyeIcon /> <p>Preview</p>
                        </div>
                    </div>
                    <div>
                        <div className={`${styles.button} ${styles.save}`} onClick={handleSave}>
                            <DoneIcon /> <p>Save</p>
                        </div>
                    </div>
                </div>

                <Grid container sx={{ backgroundColor: '#F5F5F5', padding: '50px 20px 20px 20px' }}>
                    <Grid item xs={12} md={3.5}>
                        <Box
                            component="img"
                            src={UMBRACO_ADRESS + image[0].url}
                            alt="Fashion"
                            sx={{
                                width: "100%",
                                borderRadius: "8px",
                                border: "1px solid #ddd",
                                objectFit: "cover",
                                marginBottom: '20px',
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} md={8} pl={3}>
                        <Box component="form" sx={{ mb: 4 }}>
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Name
                            </Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                sx={{ mb: 2 }}
                                value={updatedFields.photographerName}
                                onChange={(e) => handleChangeField("photographerName", e.target.value)}
                            />
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Description
                            </Typography>
                            <TextareaAutosize
                                minRows={6}
                                style={{
                                    width: "100%",
                                    padding: "16px",
                                    borderRadius: "4px",
                                    border: "1px solid #ccc",
                                    backgroundColor: 'transparent',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '16px'
                                }}
                                value={updatedFields.description}
                                onChange={(e) => handleChangeField("description", e.target.value)}
                            />
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Website
                            </Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                sx={{ mb: 2 }}
                                value={updatedFields.websiteLink}
                                onChange={(e) => handleChangeField("websiteLink", e.target.value)}
                            />
                        </Box>
                    </Grid>

                    <TableContainer component={Paper} sx={{ backgroundColor: 'transparent', maxWidth: '400px' }}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>City</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {cityList.map((city: string | number | boolean | ReactElement<any, string | JSXElementConstructor<any>> | Iterable<ReactNode> | ReactPortal | null | undefined, index: Key | null | undefined) => (
                                    <TableRow key={index}>
                                        <TableCell>{city}</TableCell>
                                    </TableRow>
                                ))}
                                <TableRow>
                                    <TableCell>
                                        <TextField
                                            value={newCity}
                                            onChange={(e) => setNewCity(e.target.value)}
                                            placeholder="Enter city name"
                                            fullWidth
                                            variant="outlined"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Button variant="contained" onClick={handleAddCity}>
                                            Add
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
            </div>
            <SaveConfirmationModal
                open={isSaveModalOpen}
                onClose={() => setIsSaveModalOpen(false)}
            />
            <DeleteConfirmationModal
                open={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleDelete}
            />
        </>
    );
};

export default PhotographerComponent;

