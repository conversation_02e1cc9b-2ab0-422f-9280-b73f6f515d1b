import { useState } from "react";
import { Modal, Box, Button, IconButton, Typography, Select, MenuItem } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import styles from "./uploadModal.module.scss";
import FileUploadPhoto from "../fileUploadPhoto/FileUploadPhoto";
import useGetShopPageData from "../../api/useGetShopPageData";

interface UploadModalProps {
  open: boolean;
  onClose: () => void;
  id: string;
}

const UploadModal: React.FC<UploadModalProps> = ({ open, onClose, id }) => {
  return (
    <Modal open={open} onClose={onClose}>
      <Box className={styles.modalContainer}>
        <div className={styles.modalHeader}>
          <h2>Upload File</h2>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </div>
        <div className={styles.uploadContainer}>
          <FileUploadPhoto id={id} />
        </div>
      </Box>
    </Modal>
  );
};

export default UploadModal;
