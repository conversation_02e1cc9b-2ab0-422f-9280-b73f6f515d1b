import React from "react";
import { Modal, Box, Typography, Button } from "@mui/material";

interface ResizeModalProps {
  open: boolean;
  onReload: () => void;
}

const ResizeModal = ({ open, onReload }: ResizeModalProps) => {
  return (
    <Modal
      open={open}
      aria-labelledby="resize-modal-title"
      aria-describedby="resize-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "#4D4D4D",
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
          width: 400,
        }}
      >
        <Typography
          id="resize-modal-title"
          variant="h6"
          component="h2"
          sx={{ color: "#fff" }}
        >
          Window Size Changed
        </Typography>
        <Typography
          id="resize-modal-description"
          sx={{ mt: 1, mb: 2, color: "#808080" }}
        >
          The window size has changed. Please refresh the page to continue.
        </Typography>
        <Button
          variant="contained"
          onClick={onReload}
          sx={{
            mt: 2,
            bgcolor: "#808080",
            border: '2px solid #808080',
            color: '#fff',
            "&:hover": { bgcolor: "#fff", color: '#808080', border: '2px solid #808080' },
          }}
        >
          Refresh
        </Button>
      </Box>
    </Modal>
  );
};

export default ResizeModal;
