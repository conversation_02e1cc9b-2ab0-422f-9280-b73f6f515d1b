import { useRef, useState } from "react";
import styles from "./fileUpload.module.scss";
import DescriptionIcon from "@mui/icons-material/Description";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import useUploadFile from "../../api/useUploadFile";
import SlideshowIcon from "@mui/icons-material/Slideshow";
import { CircularProgress } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";

type UploadStatus = "select" | "uploading" | "done" | "error";

interface FileUploadProps {
  onFileSelect?: (file: File) => void;
  onUploadSuccess?: (mediaUrl: string) => void;
}

const FileUpload = ({ onFileSelect, onUploadSuccess }: FileUploadProps) => {
  const { mutate: uploadFile } = useUploadFile();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("select");
  const [isDragOver, setIsDragOver] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const allowedVideoTypes = [
    "video/mp4",
    "video/webm",
    "video/ogg",
    "video/quicktime",
    "video/x-msvideo",
    "video/x-flv",
    "video/x-matroska",
  ];

  const isVideoFile = (file: File): boolean => {
    return allowedVideoTypes.includes(file.type);
  };

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      if (!isVideoFile(file)) {
        setErrorMessage("Only video files are allowed");
        setUploadStatus("error");
        return;
      }

      setErrorMessage(null);
      setSelectedFile(file);
      setUploadStatus("select");
      if (onFileSelect) onFileSelect(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>): void => {
    event.preventDefault();
    setIsDragOver(false);
    if (event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];

      if (!isVideoFile(file)) {
        setErrorMessage("Only video files are allowed");
        setUploadStatus("error");
        return;
      }

      setErrorMessage(null);
      setSelectedFile(file);
      setUploadStatus("select");
      if (onFileSelect) onFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>): void => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (): void => {
    setIsDragOver(false);
  };

  const onChooseFile = (): void => {
    inputRef.current?.click();
  };

  const clearFileInput = (): void => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    setSelectedFile(null);
    setProgress(0);
    setUploadStatus("select");
    setErrorMessage(null);
  };

  const handleUpload = (): void => {
    if (!selectedFile) return;

    setUploadStatus("uploading");

    uploadFile(selectedFile, {
      onSuccess: (response: any) => {
        const mediaUrl = response.mediaUrl;
        setUploadStatus("done");
        if (onUploadSuccess) {
          onUploadSuccess(mediaUrl);
        }
      },
      onError: () => {
        setUploadStatus("select");
      },
      onSettled: () => {
        setProgress(100);
      },
    });
  };

  return (
    <div className={styles.fileUploadContainer}>
      <div
        className={styles.dropArea}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={inputRef}
          type="file"
          accept="video/*"
          onChange={handleFileChange}
        />

        {!selectedFile && (
          <>
            <button className={styles.fileBtn} onClick={onChooseFile}>
              <SlideshowIcon />
              <p>Drag a video here or upload a file</p>
            </button>
          </>
        )}

        {errorMessage && uploadStatus === "error" && (
          <div className={styles.errorMessage}>
            <ErrorOutlineIcon />
            <p>{errorMessage}</p>
            <button
              onClick={() => {
                setErrorMessage(null);
                setUploadStatus("select");
              }}
            >
              <CloseIcon className={styles.closeIcon} />
            </button>
          </div>
        )}

        {selectedFile && (
          <div className={styles.fileCardContainer}>
            <div className={styles.fileCard}>
              <DescriptionIcon className={styles.icon} />

              <div className={styles.fileInfo}>
                <div style={{ flex: 1 }}>
                  <h6>{selectedFile?.name}</h6>

                  <div className={styles.progressBg}>
                    <div
                      className={styles.progress}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>

                {uploadStatus === "select" ? (
                  <button onClick={clearFileInput}>
                    <CloseIcon className={styles.closeIcon} />
                  </button>
                ) : (
                  <div className={styles.checkCircle}>
                    {uploadStatus === "uploading" ? (
                      `${progress}%`
                    ) : uploadStatus === "done" ? (
                      <CheckCircleIcon style={{ fontSize: "20px" }} />
                    ) : null}
                  </div>
                )}
              </div>
            </div>
            {uploadStatus === "uploading" ? (
              <div className={styles.uploadLoader}>
                <CircularProgress size={24} />
              </div>
            ) : (
              <button
                className={styles.uploadBtn}
                onClick={handleUpload}
                disabled={uploadStatus === "done"}
                style={
                  uploadStatus === "done"
                    ? { backgroundColor: "#8DC63F" }
                    : undefined
                }
              >
                {uploadStatus === "select" ? "Upload" : "Done"}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUpload;
