import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useSaveState() {
  const [isSavingState, setIsSaving] = useState(false);
  const [errorState, setError] = useState<Error | null>(null);

  const sendSaveState = async () => {
    setIsSaving(true);
    setError(null);

    console.log("sendSaveState called");

    try {
      sendMessage(
          wsMessages.saveState,{}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsSaving(false);
    }
  };

  return { sendSaveState, isLoadingState: isSavingState, errorState };
}
