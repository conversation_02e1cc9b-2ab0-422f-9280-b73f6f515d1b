import { useState, useEffect } from "react";
import { sendMessage } from "../websockets/wsClient";
import { wsMessages } from "../websockets/wsMessages";
import { addListener } from "../websockets/wsClient";
import { wsEvents } from "../websockets/wsEvents";

export interface IVideoInfo {
    date: string;
    title: string;
    client: string;
    videographer: string;
}

export const useVideoInfo = (initialVideoInfo: IVideoInfo) => {
    const [videoInfo, setVideoInfo] = useState<IVideoInfo>(initialVideoInfo);

    const handleChange = (field: keyof IVideoInfo, value: string) => {
        setVideoInfo((prev) => ({ ...prev, [field]: value }));
    };

    const handleBlur = () => {
        sendUpdateToBackend();
    };

    const sendUpdateToBackend = () => {
        try {
            sendMessage(wsMessages.updateVideoInfo,
                videoInfo
            );
        } catch (error) {
            console.error("Error sending video info update:", error);
        }
    };

    useEffect(() => {
        const handleVideoInfoResponse = (data: any) => {
            if (data && data.data) {
                const videoInfoData = data.data;

                const updatedInfo: Partial<IVideoInfo> = {};

                if (videoInfoData.date !== undefined) updatedInfo.date = videoInfoData.date;
                if (videoInfoData.title !== undefined) updatedInfo.title = videoInfoData.title;
                if (videoInfoData.client !== undefined) updatedInfo.client = videoInfoData.client;
                if (videoInfoData.videographer !== undefined) updatedInfo.videographer = videoInfoData.videographer;

                if (Object.keys(updatedInfo).length > 0) {
                    setVideoInfo(prev => ({ ...prev, ...updatedInfo }));
                }
            }
        };

        addListener(wsEvents.videoInfoUpdated, handleVideoInfoResponse);

        return () => {
        };
    }, []);

    return { 
        videoInfo, 
        setVideoInfo, 
        handleChange, 
        handleBlur,
        sendUpdateToBackend
    };
};
