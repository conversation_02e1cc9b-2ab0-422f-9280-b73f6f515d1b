import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ield, Button, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import styles from "./sidebarPanel.module.scss";
import { PhotoItem } from "../../types/polygons.type";

interface SidebarFormProps {
  open: boolean;
  onClose: () => void;
  handleSave: (item: PhotoItem) => void;
  item: PhotoItem;
}

const SidebarForm: React.FC<SidebarFormProps> = ({
  open,
  onClose,
  handleSave,
  item,
}) => {
  const [formData, setFormData] = useState(item);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (open && item) {
      setFormData(item);
    }
  }, [open, item]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setFormData({ ...formData, thumbnail: event.target.files[0] });
      clearFieldError("thumbnail");
    }
  };

  const handleThumbnailClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFormData({ ...formData, thumbnail: e.dataTransfer.files[0] });
      clearFieldError("thumbnail");
    }
  };
  const validateFields = () => {
    const errors: { [key: string]: string } = {};
    const requiredFields: (keyof PhotoItem)[] = [
      "productName",
      "displayedName",
      "brand",
      "link",
      "zIndex",
    ];

    requiredFields.forEach((field) => {
      if (!formData[field]) {
        errors[field] = "This field is required";
      }
    });

    if (!formData.thumbnail && !formData.thumbnailKey) {
      errors[`thumbnail`] = "This field is required";
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const clearFieldError = (fieldName: string) => {
    if (fieldErrors[fieldName]) {
      setFieldErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const handleSubmit = () => {
    if (!validateFields()) return;
    handleSave(formData);
    onClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <div className={styles.sidebar}>
        <div className={styles.topSecion}>
          <div className={styles.header}>
            <h6>BLOCK EDITOR</h6>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </div>

          <div className={styles.formContainer}>
            <div className={styles.thumbnailContainer}>
              <p className={styles.thumbnailTitle}>Thumbnail</p>
              <div className={styles.thumbnailErrorContainer}>
                <div
                  className={`${styles.thumbnailArea} ${
                    dragActive ? styles.active : ""
                  } ${fieldErrors[`thumbnail`] ? styles.thumbnailError : ""}`}
                  onClick={handleThumbnailClick}
                  onDragOver={handleDragOver}
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {formData?.thumbnail || formData?.thumbnailUrl ? (
                    <img
                      src={
                        formData?.thumbnail
                          ? URL.createObjectURL(formData.thumbnail)
                          : formData?.thumbnailUrl
                      }
                      alt="Thumbnail"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  ) : (
                    <div className={styles.icon}>
                      <AddIcon />
                      <p>Choose</p>
                    </div>
                  )}
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    style={{ display: "none" }}
                    accept="image/*"
                  />
                </div>
                <p className={styles.error}>{fieldErrors.thumbnail}</p>
              </div>
            </div>

            <div className={styles.form}>
              <div className={styles.textFieldContainer}>
                <p>Product Name</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="productName"
                  onChange={(e) => {
                    handleChange(e);
                    clearFieldError("productName");
                  }}
                  value={formData?.productName}
                  error={!!fieldErrors.productName}
                  helperText={fieldErrors.productName}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Displayed Name</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="displayedName"
                  onChange={(e) => {
                    handleChange(e);
                    clearFieldError("displayedName");
                  }}
                  value={formData?.displayedName}
                  error={!!fieldErrors.displayedName}
                  helperText={fieldErrors.displayedName}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Brand</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="brand"
                  onChange={(e) => {
                    handleChange(e);
                    clearFieldError("brand");
                  }}
                  value={formData?.brand}
                  error={!!fieldErrors.brand}
                  helperText={fieldErrors.brand}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Link</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="link"
                  onChange={(e) => {
                    handleChange(e);
                    clearFieldError("link");
                  }}
                  value={formData?.link}
                  error={!!fieldErrors.link}
                  helperText={fieldErrors.link}
                />
              </div>
              <div className={styles.textFieldContainer}>
                <p>Zindex</p>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="zIndex"
                  onChange={(e) => {
                    handleChange(e);
                    clearFieldError("zIndex");
                  }}
                  value={formData?.zIndex}
                  error={!!fieldErrors.zIndex}
                  helperText={fieldErrors.zIndex}
                />
              </div>
            </div>
          </div>
        </div>

        <div className={styles.actions}>
          <Button className={styles.cancel} onClick={onClose}>
            Cancel
          </Button>
          <Button className={styles.submit} onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default SidebarForm;
