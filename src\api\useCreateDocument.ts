import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/document`;

interface DocumentData {
  template: { id: string };
  parent: { id: string };
  documentType: { id: string };
  values: Array<{
    editorAlias: string;
    alias: string;
    culture: string | null;
    segment: string | null;
    value: any;
  }>;
  variants: Array<{
    culture: string | null;
    segment: string | null;
    state: string;
    name: string;
  }>;
}

const useCreateDocumentWithFile = () => {
  const { token, fetchToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const createDocument = async (data: DocumentData) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    if (!token) {
      await fetchToken();
      return;
    }

    try {
      const response = await fetch(API_URL, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        if (response.status === 401) {
          await fetchToken();
        }
        throw new Error(`Error: ${response.statusText}`);
      }

      setSuccess(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { createDocument, loading, error, success };
};

export default useCreateDocumentWithFile;
