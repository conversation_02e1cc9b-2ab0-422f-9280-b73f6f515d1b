import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useStopTracking() {
  const [isLoadingStopTracking, setIsLoading] = useState(false);
  const [errorStopTracking, setError] = useState<Error | null>(null);

  const sendStopTracking = async () => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.stopTracking,
          null
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendStopTracking, isLoadingStopTracking, errorStopTracking };
}
