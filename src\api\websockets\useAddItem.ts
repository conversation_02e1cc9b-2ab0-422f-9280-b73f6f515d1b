import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useAddItem() {
  const [isLoadingItem, setIsLoading] = useState(false);
  const [errorItem, setError] = useState<Error | null>(null);

  const sendAddObject = async () => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.addObject,
          null
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendAddObject, isLoadingItem, errorItem };
}
