import { useState, useEffect } from 'react';
import axios from 'axios';
import { HOST_ADRESS } from '../constants/urls';

export function useGetAllObjects(sessionId: string) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!sessionId) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);

        const response = await axios.get(`${HOST_ADRESS}/video/get_all_objects`, {
          headers: {
            'Session-Id': sessionId,
          },
        });

        setData(response.data.json());
      } catch (err: any) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sessionId]);

  return { data, loading, error };
}
