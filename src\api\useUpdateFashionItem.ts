import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const useUpdateFashionItem = () => {
  const { token, fetchToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const updateFashionItem = async (
    id: string,
    input: any,
    temporaryFileId: string | null,
    data: {
      title: string;
      description: string;
      photographer: string;
      date: string;
      city: string;
      other: string;
      facebook: string;
      mail: string;
      mediaKey: string;
      addItem?: Array<{
        minatureImageKey: string;
        affiliateLink: string;
        productName: string;
        displayedName: string;
        brand: string;
        zIndex: number;
      }>;
    }
  ) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    let currentToken = token;
    if (!currentToken) {
      await fetchToken();
      currentToken = localStorage.getItem("authToken") || "";
      if (!currentToken) {
        setError("Missing authentication token after refresh.");
        setLoading(false);
        return;
      }
    }
    const jsonString = JSON.stringify(input);
    const utf8Bytes = new TextEncoder().encode(jsonString);
    const binary = Array.from(utf8Bytes)
      .map((byte) => String.fromCharCode(byte))
      .join("");
    const base64 = btoa(binary);

    const requestBody = {
      values: [
        {
          editorAlias: "Umbraco.MediaPicker3",
          alias: "shopPagePhoto",
          culture: null,
          segment: null,
          value: [
            {
              key: id,
              mediaKey: data.mediaKey,
              mediaTypeAlias: "",
              crops: [],
              focalPoint: null,
            },
          ],
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "models",
          culture: null,
          segment: null,
          value: data.title,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "date",
          culture: null,
          segment: null,
          value: data.date,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "location",
          culture: null,
          segment: null,
          value: data.city,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "description",
          culture: null,
          segment: null,
          value: data.description,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "socials",
          culture: null,
          segment: null,
          value: data.other,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "facebook",
          culture: null,
          segment: null,
          value: data.facebook,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "mail",
          culture: null,
          segment: null,
          value: data.mail,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "photographer",
          culture: null,
          segment: null,
          value: data.photographer,
        },
        data.addItem
          ? {
              editorAlias: "Umbraco.BlockList",
              alias: "addItem",
              culture: null,
              segment: null,
              value: {
                layout: {
                  "Umbraco.BlockList": data.addItem
                    ? data.addItem.map(() => ({
                        contentKey: "b4bb0c68-236a-475d-ab90-f2579ac41c49",
                      }))
                    : [],
                },
                contentData: data.addItem
                  ? data.addItem.map((item) => ({
                      key: "b4bb0c68-236a-475d-ab90-f2579ac41c49",
                      contentTypeKey: "c54ec5a4-edb7-4d85-8621-317429571204",
                      values: [
                        {
                          culture: null,
                          segment: null,
                          alias: "minatureImage",
                          editorAlias: "Umbraco.MediaPicker3",
                          value: [
                            {
                              key: id,
                              mediaKey: item.minatureImageKey,
                              mediaTypeAlias: "Image",
                              crops: [],
                              focalPoint: null,
                            },
                          ],
                        },
                        {
                          culture: null,
                          segment: null,
                          alias: "brand",
                          editorAlias: "Umbraco.TextBox",
                          value: item.brand,
                        },
                        {
                          culture: null,
                          segment: null,
                          alias: "affiliateLink",
                          editorAlias: "Umbraco.TextBox",
                          value: item.affiliateLink,
                        },
                        {
                          culture: null,
                          segment: null,
                          alias: "productName",
                          editorAlias: "Umbraco.TextBox",
                          value: item.productName,
                        },

                        {
                          culture: null,
                          segment: null,
                          alias: "displayedName",
                          editorAlias: "Umbraco.TextBox",
                          value: item.displayedName,
                        },

                        {
                          culture: null,
                          segment: null,
                          alias: "zIndex",
                          editorAlias: "Umbraco.Integer",
                          value: item.zIndex,
                        },
                      ],
                    }))
                  : [],
              },
            }
          : null,
        temporaryFileId
          ? {
              editorAlias: "Umbraco.UploadField",
              alias: "uploadVideoMap",
              culture: null,
              segment: null,
              value: {
                src: `data:application/json;base64,${base64}`,
                temporaryFileId,
              },
            }
          : null,
      ].filter(Boolean),
      variants: [
        {
          culture: "en-US",
          segment: null,
          state: "PublishedPendingChanges",
          name: data.title,
        },
      ],
      template: {
        id: "b4f97ab6-ad89-4aa8-9f3a-f42a17d3eac3",
      },
    };

    try {
      let response = await fetch(
        `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${currentToken}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          await fetchToken();
          currentToken = localStorage.getItem("authToken") || "";
          response = await fetch(
            `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${currentToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );
        }
        if (!response.ok) {
          throw new Error(`Błąd: ${response.statusText}`);
        }
      }

      setSuccess(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { updateFashionItem, loading, error, success };
};

export default useUpdateFashionItem;
