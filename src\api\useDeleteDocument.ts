import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/document`;

const useDeleteDocument = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const deleteDocument = async (id: string) => {
        if (!id) {
            setError("Error: Document ID is required");
            return;
        }

        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        try {
            let response = await fetch(`${API_URL}/${id}`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}`, {
                        method: "DELETE",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                    });
                }
                if (!response.ok) {
                    throw new Error(`Error: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { deleteDocument, loading, error, success };
};

export default useDeleteDocument;
