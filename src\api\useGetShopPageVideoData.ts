import { useQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS } from '../constants/urls';

const fetchAddShopPageVideo = async () => {
  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/delivery/api/v2/content?filter=contentType:addShopPageClique&take=1000000`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch shop page data');
  }

  const data = await response.json();

  return data.items;
};

const useGetShopPageVideoData = () => {
  return useQuery({
    queryKey: ['addShopPageClique'],
    queryFn: fetchAddShopPageVideo,
  });
};

export default useGetShopPageVideoData;
