import React, { useEffect, useRef, useState } from "react";

// Typing for words
interface Word {
    text: string;
    weight: number;
}

interface WordCloudProps {
    words: Word[];
}

const WordCloudComponent: React.FC<WordCloudProps> = ({ words }) => {
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const [canvasWidth, setCanvasWidth] = useState<number>(window.innerWidth);

    useEffect(() => {
        const handleResize = () => {
            setCanvasWidth(window.innerWidth);
        };

        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    useEffect(() => {
        const drawWordCloud = () => {
            const canvas = canvasRef.current;
            if (!canvas) return;

            const ctx = canvas.getContext("2d");
            if (!ctx) return;

            const width = (canvas.width = Math.min(canvasWidth, 1200)); // Dynamically adjust canvas width
            const height = (canvas.height = Math.min(canvasWidth * 0.5, 600)); // Adjust height proportionally

            // Clear the canvas
            ctx.clearRect(0, 0, width, height);

            // Center point
            const centerX = width / 2;
            const centerY = height / 2;

            const positions: { x: number; y: number; width: number; height: number }[] = [];

            // Sort words by weight (largest to smallest)
            const sortedWords = [...words].sort((a, b) => b.weight - a.weight);

            // Scale factor based on screen width
            const scaleFactor = Math.min(1, canvasWidth / 1200); // Limit scaling to avoid overly large fonts

            // Draw each word
            sortedWords.forEach((word, index) => {
                const fontSize = word.weight * 5 * scaleFactor; // Scale font size
                ctx.font = `${fontSize}px Arial`;
                ctx.fillStyle = `hsl(0, 0%, ${Math.random() * 30 + 40}%)`; // Greyish colors (40%-70% lightness)

                const textWidth = ctx.measureText(word.text).width;
                const textHeight = fontSize;

                let x = centerX;
                let y = centerY;

                if (index === 0) {
                    // Place the largest word at the center
                    x -= textWidth / 2;
                    y += textHeight / 2;
                } else {
                    // Place words around the largest word
                    let angle = 0; // Start from 0 degrees
                    let radius = 160 * scaleFactor; // Larger initial radius for spacing

                    let collision = true;
                    while (collision) {
                        x = centerX + radius * Math.cos(angle) - textWidth / 2;
                        y = centerY + radius * Math.sin(angle) + textHeight / 2;

                        // Check if this position collides with any previously placed word
                        collision = positions.some((pos) => {
                            const padding = 13 * scaleFactor; // Additional padding between words
                            return (
                                x < pos.x + pos.width + padding &&
                                x + textWidth + padding > pos.x &&
                                y < pos.y + pos.height + padding &&
                                y + textHeight + padding > pos.y
                            );
                        });

                        // Adjust the spiral
                        angle += 0.2; // Increase the angle more for faster positioning
                        radius += 0.8 * scaleFactor; // Adjust spiral growth
                    }
                }

                // Save the position
                positions.push({ x, y, width: textWidth, height: textHeight });

                // Draw the word
                ctx.fillText(word.text, x, y);
            });
        };

        drawWordCloud();
    }, [words, canvasWidth]);

    return (
        <div style={{ textAlign: "center" }}>
            <canvas ref={canvasRef} />
        </div>
    );
};

export default WordCloudComponent;
