@import '../../styles/variables';

.movieGridContainer {
  max-width: 100%;
  background-color: $gray;
  padding: 30px 15px;
  margin: 20px;

  .gridContainer {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    a {
      text-align: center;
      margin-right: 10px;
      border-radius: 5px;
      color: $white;
      font-weight: bold;
      cursor: pointer;
      transition: 0.3s ease;
    }

    .movieCard {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: $white;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.2s ease-in-out;
      margin: 0 auto;
      padding: 10px;

      &:hover {
        transform: translateY(-5px);
      }

      .movieImageContainer {
        position: relative;
        width: fit-content;
        max-width: 95%;

        .label {
          position: absolute;
          top: 10px;
          left: 20px;
          background-color: $red;
          z-index: 999;

          &.published {
            background-color: $green;
          }

          &.unpublished {
            background-color: $red;
          }

          p {
            color: $white;
            padding: 5px 15px;
          }
        }

        .movieImage {
          position: relative;
          height: 550px;
          width: 100%;
          object-fit: contain;
          display: block;
          margin: 0 auto;
        }
      }

      .movieDetails {
        padding: 15px;
        text-align: center;

        h3 {
          font-weight: 500;
          margin-bottom: 10px;
          color: $black;
        }

        p {
          font-weight: 500;
          margin: 5px 0;
          color: $mistGray;
        }
      }
    }
  }

  .loadMore {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $black;
    font-weight: 600;
    font-size: 24px;
    padding-top: 50px;
    cursor: pointer;

    svg {
      font-size: 48px;
    }
  }
}