import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { UMBRACO_ADRESS } from '../constants/urls';

interface UploadResponse {
  success: boolean;
  fileUrl: string;
}

const uploadFile = async (file: File): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post<UploadResponse>(
    `${UMBRACO_ADRESS}umbraco/api/MediaUpload/upload`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  return response.data;
};

const useUploadFile = () => {
  return useMutation<UploadResponse, Error, File>({
    mutationFn: uploadFile,
  });
};

export default useUploadFile;
