import { Link } from "react-router-dom"
import Header from "../../components/shared/Header"
import Sidebar from "../../components/sideBar/SideBar"
import styles from './cliqueMoviesPage.module.scss'
import MovieGrid from "../../components/movieGrid/MovieGrid"

export const CliqueMoviesPage = () => {
    return (
        <section className={styles.cliqueMoviesPage}>
            <Header />
            <div className={styles.container}>
                <div className={styles.movieGridContainer}>
                    <MovieGrid />
                </div>
                <div className={styles.sidebarContainer}>
                    <Sidebar isClique={true} />
                </div>
                <div className={styles.filters}>
                    <Link to={'/clique-add-video'} className={`${styles.videos}`}>+ New video</Link>
                </div>
                <div className={styles.emptySpace}></div>
            </div>
        </section>
    )
}