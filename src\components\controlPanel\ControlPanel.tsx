import React, { useEffect } from "react";
import { <PERSON>ton, CircularProgress } from "@mui/material";
import CloudSyncIcon from "@mui/icons-material/CloudSync";
import styles from './controlPanel.module.scss';
import { formatDate } from "../../middleware/formatDate";
import MessageAlert from "../messageAlert/MessageAlert";
import { FormData} from "../../types/formData";
import {SessionState} from "../../enums/sessionState";
import { useVideoInfo, IVideoInfo } from "../../api/useVideoInfo";

interface ControlPanelProps {
    sessionState: SessionState | null;
    sendStartTracking: (frameIdx: number) => void;
    sendStopTracking: () => void;
    setIsSyncModalOpen: (isOpen: boolean) => void;
    handleToggleVM: () => void;
    buttonStyles: object;
    data: any;
    vmError: any;
    vmStatus?: string;
    vmIsLoading: boolean;
    startMutation: {
        status: string;
        mutate: () => void;
    };
    stopMutation: {
        status: string;
        mutate: () => void;
    };
    setFormData: React.Dispatch<React.SetStateAction<FormData>>;
    formData: FormData;
    currentFrame: number;
}


export const ControlPanel = ({
    sessionState,
    sendStartTracking,
    sendStopTracking,
    setIsSyncModalOpen,
    handleToggleVM,
    buttonStyles,
    data,
    vmError,
    vmStatus,
    vmIsLoading,
    startMutation,
    stopMutation,
    formData,
    setFormData,
    currentFrame,
}: ControlPanelProps) => {

    const initialVideoInfo: IVideoInfo = {
        date: formData.date || "",
        title: formData.title || "",
        client: formData.client || "",
        videographer: formData.videographer || ""
    };

    const { videoInfo, handleChange, handleBlur } = useVideoInfo(initialVideoInfo);

    useEffect(() => {
        setFormData(prevData => ({
            ...prevData,
            date: videoInfo.date,
            title: videoInfo.title,
            client: videoInfo.client,
            videographer: videoInfo.videographer
        }));
    }, [videoInfo, setFormData]);

    const handleStartTracking = () => {
        sendStartTracking(currentFrame);
    };

    const handleStopTracking = () => {
        sendStopTracking();
    }

    return (
        <>
            {vmError && <MessageAlert type="error" message={`Error: ${vmError.message}`} />}
            <section className={styles.controlPanel}>
                <div className={styles.mlMachineStatusContainer}>
                    <div className={styles.mlMachineStatus}>
                        {(vmIsLoading ||
                            startMutation.status === 'pending' ||
                            stopMutation.status === 'pending') ? (
                            <CircularProgress size={24} color="inherit" />
                        ) : (
                            !vmIsLoading &&
                            !vmError && (
                                <>
                                    {vmStatus ? (
                                        <>
                                            <p>Connected to AI worker</p>
                                            <div className={styles.greenDot}></div>
                                        </>
                                    ) : (
                                        <>
                                            <p>No connection...</p>
                                            <div className={styles.redDot}></div>
                                        </>
                                    )}
                                </>
                            )
                        )}
                    </div>
                </div>
                <div className={styles.topArea}>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>DATE</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="date"
                                value={formData.date}
                                onChange={(e) => handleChange('date', e.target.value)}
                                onBlur={handleBlur}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>TITLE</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="title"
                                value={formData.title}
                                onChange={(e) => handleChange('title', e.target.value)}
                                onBlur={handleBlur}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>CLIENT</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="client"
                                value={formData.client}
                                onChange={(e) => handleChange('client', e.target.value)}
                                onBlur={handleBlur}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>VIDEOGRAPHER</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="videographer"
                                value={formData.videographer}
                                onChange={(e) => handleChange('videographer', e.target.value)}
                                onBlur={handleBlur}
                            />
                        </div>
                    </div>
                </div>
                <div className={styles.buttonContainer}>
                    <Button
                        variant="contained"
                        onClick={sessionState === SessionState.TRACKING ? handleStopTracking : handleStartTracking}
                        disabled={(sessionState !== SessionState.IDLE && sessionState !== SessionState.TRACKING)}
                        sx={buttonStyles}
                    >
                        {sessionState === SessionState.TRACKING ? "Stop Tracking" : "Start Tracking"}
                    </Button>
                </div>
            </section>
        </>
    );
};
