import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FUNCTION_APP_BASE_URL } from '../constants/urls';

const restartVMRequest = async () => {
  const response = await fetch(`${FUNCTION_APP_BASE_URL}/vm/restart?code=eymTv4nx0Bxt54_cdMnuaU0AK9KN-of3F4KynBVGJuwjAzFuQnHSyQ==`, {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error('Failed to restart VM');
  }

  return response.text();
};

export const useRestartVM = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => restartVMRequest(),
    onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['vmStatus'] });
    },
  });
};
