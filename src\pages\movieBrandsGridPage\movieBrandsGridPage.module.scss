@import '../../styles/variables';

.cliqueMoviesPage {
    .container {
        display: grid;
        grid-template-columns: 340px auto auto auto auto;
        grid-template-rows: auto auto auto auto auto;
        grid-column-gap: 0px;
        grid-row-gap: 0px;
        padding: 0 20px;

        .movieGridContainer {
            grid-area: 2 / 2 / 6 / 6;
        }

        .sidebarContainer {
            grid-area: 2 / 1 / 6 / 2;
        }

        .filters {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 20px;
            grid-area: 1 / 2 / 2 / 6;
            margin: 10px 20px;

            a {
                font-size: 20px;
                letter-spacing: 1px;
                font-weight: 600;
                text-align: center;
                padding: 12px 40px;
                width: calc(25% - 30px);
                margin-right: 10px;
                color: $white;
                cursor: pointer;
                transition: 0.3s ease;
                width: auto;

                &:last-of-type {
                    margin-right: 0;
                }

                &:hover {
                    filter: brightness(1.2);
                }
            }

            .videos {
                background-color: $darkGray;
            }
        }

        .emptySpace {
            grid-area: 1 / 1 / 2 / 2;
        }
    }
}