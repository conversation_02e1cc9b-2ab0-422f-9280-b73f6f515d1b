import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FUNCTION_APP_BASE_URL } from '../constants/urls';

const startVMRequest = async () => {
  const response = await fetch(`${FUNCTION_APP_BASE_URL}/vm/start?code=eymTv4nx0Bxt54_cdMnuaU0AK9KN-of3F4KynBVGJuwjAzFuQnHSyQ==`, {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error('Failed to start VM');
  }

  return response.text();
};

export const useStartVM = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => startVMRequest(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vmStatus'] });
    },
  });
};
