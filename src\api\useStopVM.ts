import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FUNCTION_APP_BASE_URL } from '../constants/urls';

const stopVMRequest = async () => {
  const response = await fetch(`${FUNCTION_APP_BASE_URL}/vm/stop?code=eymTv4nx0Bxt54_cdMnuaU0AK9KN-of3F4KynBVGJuwjAzFuQnHSyQ==`, {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error('Failed to stop VM');
  }

  return response.text();
};

export const useStopVM = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => stopVMRequest(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vmStatus'] });
    },
  });
};
