.photosPage {
  padding: 20px;
  background-color: #f9f9f9;
  min-height: 100vh;

  .container {
    display: flex;

    .form {
      width: 100%;
      padding: 0 20px;
    }
  }
}

// Form header styling
.h1 {
  text-align: center;
  font-family: "source-sans-pro", sans-serif;
  font-weight: 600;
  font-style: normal;
  margin-bottom: 30px;
  margin-top: 50px;
  color: #333;
  font-size: 2.2rem;
  position: relative;

  &:after {
    content: "";
    display: block;
    width: 60px;
    height: 3px;
    background: #8c989c;
    margin: 15px auto 0;
  }
}

// Form container styling
.submitForm {
  max-width: 800px;
  margin: 0 auto;
  padding: 30px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 20px;
  }
}

// Section header styling
.sectionHeader {
  grid-column: 1 / -1;
  margin-top: 15px;
  margin-bottom: 5px;
  border-bottom: 1px solid #e0e0e0;

  h3 {
    color: #555;
    font-family: "source-sans-pro", sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 10px;
    position: relative;
    display: inline-block;

    &:after {
      content: "";
      display: block;
      width: 100%;
      height: 2px;
      background: #8c989c;
      position: absolute;
      bottom: -11px;
    }
  }
}

// Full width elements
.fullWidth {
  grid-column: 1 / -1;
}

// Form section styling
.formSection {
  display: flex;
  flex-direction: column;
}

// File upload styling
.customFileInput {
  color: transparent;
  background: none;
  border: none;
  height: 60px;
  grid-column: 1 / -1;
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.customFileInput::-webkit-file-upload-button {
  visibility: hidden;
}

.customFileInput::before {
  content: "Upload image";
  color: white;
  display: inline-block;
  background: #8c989c;
  border-radius: 4px;
  padding: 15px 25px;
  outline: none;
  white-space: nowrap;
  cursor: pointer;
  font-size: 14px;
  font-family: "source-sans-pro", sans-serif;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.customFileInput:hover::before {
  background: #7a8589;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.customFileInput:active::before {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

// Preview area styling
.preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  font-family: "source-sans-pro", sans-serif;
  grid-column: 1 / -1;
  background-color: #f7f7f7;
  padding: 20px;
  border-radius: 6px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #555;
    font-weight: 500;
  }

  img {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
  }
}

// Textarea styling
.textareaField {
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s ease;
  font-family: "source-sans-pro", sans-serif;
  background-color: #f9f9f9;
  resize: vertical;
  min-height: 80px;
  grid-column: 1 / -1;

  &:focus {
    outline: none;
    border-color: #8c989c;
    box-shadow: 0 0 0 2px rgba(140, 152, 156, 0.2);
    background-color: white;
  }

  &:hover {
    border-color: #8c989c;
    background-color: #f5f5f5;
  }

  &::placeholder {
    color: #aaa;
  }

  &.error {
    border-color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
  }
}

// Error message styling
.errorMessage {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  font-family: "source-sans-pro", sans-serif;
  display: block;
  text-align: left;
  grid-column: 1 / -1;
}

.inputWrapper {
  position: relative;
  display: flex;
  flex-direction: column;

  // Input fields styling
  input {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: "source-sans-pro", sans-serif;
    background-color: #f9f9f9;
    position: relative;

    &:focus {
      outline: none;
      border-color: #8c989c;
      box-shadow: 0 0 0 2px rgba(140, 152, 156, 0.2);
      background-color: white;
      transform: translateY(-2px);
    }

    &:hover {
      border-color: #8c989c;
      background-color: #f5f5f5;
    }

    &::placeholder {
      color: #aaa;
    }

    // Special styling for date inputs
    &[type="date"] {
      color: #555;

      &::-webkit-calendar-picker-indicator {
        filter: invert(0.5);
        cursor: pointer;
      }
    }

    // Special styling for email inputs
    &[type="email"],
    &[type="url"] {
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238c989c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3e%3c/path%3e%3cpolyline points='22,6 12,13 2,6'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 10px center;
      background-size: 16px;
      padding-right: 35px;
    }

    &.error {
      border-color: #e74c3c;
      background-color: rgba(231, 76, 60, 0.05);
    }
  }

  // Select field styling
  select {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    height: 45px;
    background-color: #f9f9f9;
    font-family: "source-sans-pro", sans-serif;
    font-size: 14px;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238c989c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    cursor: pointer;
    color: #555;

    &:focus {
      outline: none;
      border-color: #8c989c;
      box-shadow: 0 0 0 2px rgba(140, 152, 156, 0.2);
      background-color: white;
      transform: translateY(-2px);
    }

    &:hover {
      border-color: #8c989c;
      background-color: #f5f5f5;
    }

    option {
      padding: 10px;
      background-color: white;
      color: #555;
    }

    &.error {
      border-color: #e74c3c;
      background-color: rgba(231, 76, 60, 0.05);
    }
  }
}

// Submit button styling
.fileButton {
  background: #8c989c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 14px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  font-family: "source-sans-pro", sans-serif;
  grid-column: 1 / -1;
  width: 200px;
  margin: 20px auto 0;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: #7a8589;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
}
