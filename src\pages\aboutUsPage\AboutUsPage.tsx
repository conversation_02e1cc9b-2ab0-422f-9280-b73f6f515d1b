import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from './aboutUsPage.module.scss';
import useGetAboutUsData from "../../api/useGetAboutUsData";
import { Grid, Box, Typography, TextField, Button, TableCell, TableContainer, Table, TableBody, TableRow, TableHead, Paper, TextareaAutosize } from "@mui/material";
import { Key, useState } from "react";
import useUpdateAboutUsItem from "../../api/useUpdateAboutUsItem";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DoneIcon from '@mui/icons-material/Done';
import { CLIENTAPP_FRONTEND_ADDRESS } from "../../constants/urls";
import SaveConfirmationModal from '../../components/saveConfirmationModal/SaveConfirmationModal';

const AboutUsPage = () => {
    const { data, isLoading, error } = useGetAboutUsData();
    const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
    const { updateAboutUsItem } = useUpdateAboutUsItem();

    const [updatedItems, setUpdatedItems] = useState(() =>
        data?.map((item: any) => ({
            id: item.id,
            heading: item.properties.heading.markup.replace(/<[^>]*>/g, ""),
            heading2: item.properties.secondaryHeading.markup.replace(/<[^>]*>/g, ""),
            paragraph: item.properties.paragraph.markup.replace(/<[^>]*>/g, ""),
            cities: item.properties.cities.items.map((city: any) => city.content.properties.cityname),
            photographers: item.properties.photographers.items.map((photographer: any) => photographer.content.properties.photographerName),
            newCity: "",
            newPhotographer: ""
        })) || []
    );

    const handleChangeField = (index: number, field: string, value: string) => {
        setUpdatedItems((prev: any[]) => prev.map((item, i) => i === index ? { ...item, [field]: value } : item));
    };

    const handleAddCity = (index: number) => {
        setUpdatedItems((prev: any[]) => prev.map((item, i) => {
            if (i === index && item.newCity.trim()) {
                return { ...item, cities: [...item.cities, item.newCity], newCity: "" };
            }
            return item;
        }));
    };

    const handleAddPhotographer = (index: number) => {
        setUpdatedItems((prev: any[]) => prev.map((item, i) => {
            if (i === index && item.newPhotographer.trim()) {
                return { ...item, photographers: [...item.photographers, item.newPhotographer], newPhotographer: "" };
            }
            return item;
        }));
    };

    const handleSave = async (index: number) => {
        const item = updatedItems[index];
        if (!item.id) return;
        try {
            await updateAboutUsItem(item.id, item);
            setIsSaveModalOpen(true);
        } catch (error) {
            console.error("Error updating about us item:", error);
        }
    };

    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading data" />;

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <div style={{ width: '100%' }}>
                    {updatedItems.map((item: { id: Key | null | undefined; route: { path: string; }; heading: unknown; heading2: unknown; paragraph: string | number | readonly string[] | undefined; cities: any[]; newCity: unknown; photographers: any[]; newPhotographer: unknown; }, index: number) => (
                        <div key={item.id}>
                            <div className={styles.header}>
                                <div>
                                    <div className={`${styles.button}`} onClick={() => window.open(CLIENTAPP_FRONTEND_ADDRESS + item.route.path, "_blank")}>
                                        <RemoveRedEyeIcon /> <p>Preview</p>
                                    </div>
                                </div>
                                <div>
                                    <div className={`${styles.button} ${styles.save}`} onClick={() => handleSave(index)}>
                                        <DoneIcon /> <p>Save</p>
                                    </div>
                                </div>
                            </div>

                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    <Box component="form">
                                        <Typography variant="body1">Heading</Typography>
                                        <TextField fullWidth variant="outlined" value={item.heading} onChange={(e) => handleChangeField(index, "heading", e.target.value)} />

                                        <Typography variant="body1">Heading 2</Typography>
                                        <TextField fullWidth variant="outlined" value={item.heading2} onChange={(e) => handleChangeField(index, "heading2", e.target.value)} />

                                        <Typography variant="body1">Paragraph</Typography>
                                        <TextareaAutosize
                                            minRows={6}
                                            style={{ width: "100%", padding: "16px", borderRadius: "4px", border: "1px solid #ccc", fontSize: "16px" }}
                                            value={item.paragraph}
                                            onChange={(e) => handleChangeField(index, "paragraph", e.target.value)}
                                        />
                                    </Box>
                                </Grid>
                            </Grid>
                            <div className={styles.tables}>
                                <TableContainer component={Paper} sx={{ width: '400px' }}>
                                    <Table>
                                        <TableHead>
                                            <TableRow><TableCell>City</TableCell></TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {item.cities.map((city, cityIndex) => (
                                                <TableRow key={cityIndex}><TableCell>{city}</TableCell></TableRow>
                                            ))}
                                            <TableRow>
                                                <TableCell>
                                                    <TextField value={item.newCity} onChange={(e) => handleChangeField(index, "newCity", e.target.value)} placeholder="Enter city" fullWidth variant="outlined" />
                                                </TableCell>
                                                <TableCell>
                                                    <Button variant="contained" onClick={() => handleAddCity(index)}>Add</Button>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </TableContainer>

                                <TableContainer component={Paper} sx={{ width: '400px' }}>
                                    <Table>
                                        <TableHead>
                                            <TableRow><TableCell>Photographer</TableCell></TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {item.photographers.map((photographer, photographerIndex) => (
                                                <TableRow key={photographerIndex}><TableCell>{photographer}</TableCell></TableRow>
                                            ))}
                                            <TableRow>
                                                <TableCell>
                                                    <TextField value={item.newPhotographer} onChange={(e) => handleChangeField(index, "newPhotographer", e.target.value)} placeholder="Enter photographer" fullWidth variant="outlined" />
                                                </TableCell>
                                                <TableCell>
                                                    <Button variant="contained" onClick={() => handleAddPhotographer(index)}>Add</Button>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </div>
                        </div>
                    ))}
                </div>
                <SaveConfirmationModal
                    open={isSaveModalOpen}
                    onClose={() => setIsSaveModalOpen(false)}
                />
            </div>
        </div>
    );
};

export default AboutUsPage;
