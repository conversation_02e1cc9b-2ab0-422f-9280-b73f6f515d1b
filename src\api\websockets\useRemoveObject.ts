import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useRemoveObject() {
  const [isLoadingRemoveObject, setIsLoading] = useState(false);
  const [errorRemoveObject, setError] = useState<Error | null>(null);

  const sendRemoveObject = async (objectId: number) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.removeObject,
          {"object_id": objectId}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendRemoveObject, isLoadingRemoveObject, errorRemoveObject };
}
