import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./movieBrandsGrid.module.scss";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import Loader from "../loader/Loader";
import MessageAlert from "../messageAlert/MessageAlert";
import useGetShopPageVideoData from "../../api/useGetShopPageVideoData";

const MovieBrandsGrid = () => {
  const { data, isLoading, error } = useGetShopPageVideoData();
  console.log(data)
  const [activeSort, setActiveSort] = useState<string>("newest");
  const navigate = useNavigate();

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  if (isLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  const uniqueBrands: string[] = Array.from(
    new Set(
      data?.flatMap((item: any) =>
        item.properties.addItem?.items?.map(
          (addItem: any) => addItem.content.properties.brand
        )
      ).filter(Boolean)
    )
  ).map((brand) => String(brand));

  return (
    <div className={styles.photoGrid}>
      <div className={styles.sortOptions}>
        <p>Sort by:</p>
        <span
          className={`${styles.sortOption} ${activeSort === "title-asc" ? styles.active : ""}`}
          onClick={() => handleSortChange("title-asc")}
        >
          Title (A &gt; Z)
        </span>
        <span
          className={`${styles.sortOption} ${activeSort === "title-desc" ? styles.active : ""}`}
          onClick={() => handleSortChange("title-desc")}
        >
          Title (Z &gt; A)
        </span>
        <span
          className={`${styles.sortOption} ${activeSort === "newest" ? styles.active : ""}`}
          onClick={() => handleSortChange("newest")}
        >
          Newest
        </span>
        <span
          className={`${styles.sortOption} ${activeSort === "oldest" ? styles.active : ""}`}
          onClick={() => handleSortChange("oldest")}
        >
          Oldest
        </span>
      </div>
      <div className={styles.mainContent}>
        <TableContainer component={Paper} sx={{ backgroundColor: "transparent" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Brand</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {uniqueBrands.map((brand, index) => (
                <TableRow
                  key={index}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { backgroundColor: "#f0f0f0" },
                    transition: "background-color 0.2s ease",
                  }}
                  onClick={() => navigate(`/movieBrands/${encodeURIComponent(String(brand))}`)}
                >
                  <TableCell>{String(brand)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <div className={styles.loadMore}>
          <p>MORE</p>
          <KeyboardArrowDownIcon />
        </div>
      </div>
    </div>
  );
};

export default MovieBrandsGrid;