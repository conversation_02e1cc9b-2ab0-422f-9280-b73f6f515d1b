import { useState } from "react";
import useAuth from "./useAuth";

const API_URL = "https://duetprodumbraco.azurewebsites.net/umbraco/management/api/v1/document";

const useUpdatePublishStatus = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updatePublishStatus = async (id: string) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const currentTime = new Date(Date.now() + 60000).toISOString();

        const requestBody = {
            "publishSchedules": [
                {
                    "culture": null,
                    "schedule": {
                        "publishTime": `${currentTime}`,
                        "unpublishTime": null
                    }
                }
            ]
        };

        try {
            let response = await fetch(`${API_URL}/${id}/publish`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}/publish`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    });
                }
                if (!response.ok) {
                    throw new Error(`Error: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updatePublishStatus, loading, error, success };
};

export default useUpdatePublishStatus;
