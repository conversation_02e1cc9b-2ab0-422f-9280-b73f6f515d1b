export const addAlphaToHexColor = (hexColor: string, alpha: number): string => {
    if (alpha < 0 || alpha > 1) {
      throw new Error("Alpha value must be between 0 and 1.");
    }
  
    const hex = hexColor.replace("#", "");
  
    if (hex.length !== 6) {
      throw new Error("Invalid hex color format. Must be 6 characters long.");
    }
  
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
  
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };
  