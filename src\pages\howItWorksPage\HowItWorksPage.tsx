import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from './howItWorksPage.module.scss';
import useGetHowItWorksData from "../../api/useGetHowItWorksData";
import { Grid, Box, Typography, TextField } from "@mui/material";
import { useState } from "react";
import Loader from "../../components/loader/Loader";
import useUpdateHowItWorksItem from "../../api/useUpdateHowItWorksItem";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DoneIcon from '@mui/icons-material/Done';
import { CLIENTAPP_FRONTEND_ADDRESS } from "../../constants/urls";
import SaveConfirmationModal from '../../components/saveConfirmationModal/SaveConfirmationModal';

const HowItWorksPage = () => {
    const { data, isLoading, error } = useGetHowItWorksData();
    const { updateHowItWorksItem } = useUpdateHowItWorksItem();
    const [fields, setFields] = useState<Record<string, string>>({});
    const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);

    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading data" />;

    const handleFieldChange = (alias: string, value: string) => {
        setFields(prev => ({ ...prev, [alias]: value }));
    };

    const handleSave = async () => {
        const id = '620b75e8-7ac1-4601-9b22-8602abba1218';

        try {
            await updateHowItWorksItem(id, {
                heading: fields.heading || "",
                paragraph: fields.paragraph || "",
                paragraph2: fields.paragraph2 || "",
                heading2: fields.heading2 || "",
                paragraph3: fields.paragraph3 || "",
                paragraph4: fields.paragraph4 || "",
                heading3: fields.heading3 || "",
                paragraph5: fields.paragraph5 || "",
            });
            setIsSaveModalOpen(true);
        } catch (error) {
            console.error("Error updating about us item:", error);
        }
    };

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <div style={{ width: '100%' }}>
                    <div className={styles.header}>
                        <div>
                            <div className={`${styles.button}`} onClick={() => window.open(CLIENTAPP_FRONTEND_ADDRESS + 'how-it-works', "_blank")}>
                                <RemoveRedEyeIcon /> <p>Preview</p>
                            </div>
                        </div>
                        <div>
                            <div className={`${styles.button} ${styles.save}`} onClick={handleSave}>
                                <DoneIcon /> <p>Save</p>
                            </div>
                        </div>
                    </div>
                    {data && data.map((item: any) => (
                        <Grid container spacing={2} key={item.id} sx={{ mb: 4 }}>
                            <Grid item xs={12} md={12} pl={12}>
                                <Box component="form" sx={{ mb: 4 }}>
                                    <EditableField label="Heading" alias="heading" markup={item.properties?.heading?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph" alias="paragraph" markup={item.properties?.paragraph?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph 2" alias="paragraph2" markup={item.properties?.paragraph2?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Heading 2" alias="heading2" markup={item.properties?.heading2?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph 3" alias="paragraph3" markup={item.properties?.paragraph3?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph 4" alias="paragraph4" markup={item.properties?.paragraph4?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Heading 3" alias="heading3" markup={item.properties?.heading3?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph 5" alias="paragraph5" markup={item.properties?.paragraph5?.markup} onChange={handleFieldChange} />
                                </Box>
                            </Grid>
                        </Grid>
                    ))}
                </div>
                <SaveConfirmationModal
                    open={isSaveModalOpen}
                    onClose={() => setIsSaveModalOpen(false)}
                />
            </div>
        </div>
    );
};

const EditableField = ({ label, alias, markup, onChange }: { label: string; alias: string; markup: string | null; onChange: (alias: string, value: string) => void; }) => {
    const [value, setValue] = useState(markup?.replace(/<[^>]*>/g, "") || "");

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
        setValue(e.target.value);
        onChange(alias, e.target.value);
    };

    return (
        <div style={{ marginBottom: "16px" }}>
            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>{label}</Typography>
            <TextField fullWidth variant="outlined" value={value} onChange={handleChange} />
        </div>
    );
};

export default HowItWorksPage;
