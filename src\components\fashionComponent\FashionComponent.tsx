import styles from "./fashionComponent.module.scss";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import useDeleteDocument from "../../api/useDeleteDocument";
import DoneIcon from "@mui/icons-material/Done";
import { Button, TextareaAutosize, TextField, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import SidebarForm from "./SidePanel";
import DeleteConfirmationModal from "../deleteConfirmationModal/DeleteConfirmationModal";
import SaveConfirmationModal from "../saveConfirmationModal/SaveConfirmationModal";
import useUpdateFashionItem from "../../api/useUpdateFashionItem";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import {
  createPhotoItem,
  PhotoItem,
  PhotoSelection,
} from "../../types/polygons.type";
import CanvasOverImage from "./canvasOverImage";
import { useGetPolygonsOnImage } from "../../api/useGetPolygonsOnImage";
import useUnpublishDocument from "../../api/useUnpublishDocument";
import usePublishDocument from "../../api/usePublishDocument";
import Loader from "../loader/Loader";
import PublishSwitch from "../publishSwitch/publishSwitch";
import addPhotoIcon from "../../assets/images/upload-photo.png";
import { useCreateTemporaryFile } from "../../api/useCreateTemporaryFile";
import useFetchJson from "../../api/useFetchJson";
import { fetchPhotoById } from "../../api/useGetPhotoById";
import useAuth from "../../api/useAuth";
import { createAndUpload } from "../../api/useCreateAndUpload";

const commonInputSx = {
  "& .MuiOutlinedInput-root": {
    color: "#4D4D4D",
    fontFamily: "Poppins, sans-serif",
    fontSize: "16px",
    backgroundColor: "transparent",
    "& fieldset": {
      borderColor: "#808080",
    },
    "&:hover fieldset": {
      borderColor: "#808080",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#4D4D4D",
    },
  },
};

const FashionComponent = ({ item, photoUrl }: any) => {
  const { token } = useAuth();
  const getValueByAlias = (alias: string) =>
    item?.values.find((v: any) => v.alias === alias)?.value ?? "";

  const description = getValueByAlias("description");
  const photographerValue = getValueByAlias("photographer");
  const dateValue = getValueByAlias("date");
  const locationValue = getValueByAlias("location");
  const mailValue = getValueByAlias("mail");
  const titleValue = getValueByAlias("models");
  const facebookValue = getValueByAlias("facebook");
  const socialsValue = getValueByAlias("socials");
  const media = getValueByAlias("shopPagePhoto");
  const imageMediaKey =
    Array.isArray(media) && media.length > 0 ? media[0].mediaKey : null;
  const itemsValue = getValueByAlias("addItem").contentData;
  const jsonValue = getValueByAlias("uploadVideoMap").src;

  useEffect(() => {
    const fetchData = async () => {
      if (itemsValue && jsonValue) {
        try {
          const data = await fetchJson(jsonValue);

          const newItems = await Promise.all(
            itemsValue.map(async (item: any, index: number) => {
              const getValueByAliasInProducts = (alias: string) =>
                item?.values.find((v: any) => v.alias === alias)?.value ?? "";

              const link = getValueByAliasInProducts("affiliateLink");
              const productName = getValueByAliasInProducts("productName");
              const displayedName = getValueByAliasInProducts("displayedName");
              const brand = getValueByAliasInProducts("brand");
              const zIndex = getValueByAliasInProducts("zIndex");
              const minatureImage = getValueByAliasInProducts("minatureImage");

              const imageKey =
                minatureImage.length > 0 ? minatureImage[0].mediaKey : "";

              let imageUrl = "";
              if (token) {
                try {
                  const photo = await fetchPhotoById(imageKey, token);
                  if (photo) {
                    imageUrl = photo.urls?.[0]?.url ?? "";
                  }
                } catch (err) {
                  console.warn("Błąd pobierania zdjęcia:", err);
                }
              }

              const filteredData = Array.isArray(data)
                ? data.filter(
                    (d) =>
                      d.displayedName === displayedName &&
                      d.productName === productName &&
                      d.brand === brand &&
                      d.link === link
                  )
                : [];

              const polygons =
                filteredData.length > 0 ? filteredData[0].polygons : [];
              const selections =
                filteredData.length > 0 ? filteredData[0].selections : [];

              return createPhotoItem({
                objectId: index,
                thumbnailUrl: imageUrl,
                thumbnailKey: imageKey,
                link,
                productName,
                displayedName,
                brand,
                polygons,
                selections,
                zIndex,
              });
            })
          );

          setItems(newItems.sort((a, b) => a.zIndex - b.zIndex));
          setNextId(itemsValue.length + 1);
        } catch (error) {
          console.error("Błąd podczas pobierania danych:", error);
        }
      }
    };

    fetchData();
  }, [itemsValue, jsonValue, token]);

  const published = item?.variants?.[0]?.state === "Published";

  const [title, setTitle] = useState(titleValue);
  const [urlSlug, setUrlSlug] = useState(item?.urls?.[0]?.url || "");
  const [desc, setDesc] = useState(description);
  const [photographer, setPhotographer] = useState(photographerValue);

  const [fullName, setFullName] = useState("");
  const [date, setDate] = useState(dateValue);
  const [city, setCity] = useState(locationValue);
  const [mail, setMail] = useState(mailValue);
  const [ig, setIg] = useState("");
  const [fb, setFb] = useState(facebookValue);
  const [tiktok, setTiktok] = useState("");
  const [other, setOther] = useState(socialsValue);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
  const [isSidebarOpen, setSidebarOpen] = useState<number | null>(null);
  const [isPublished, setIsPublished] = useState(published);

  const [image, setImage] = useState<File | null>(null);
  const [dragging, setDragging] = useState(false);
  const [error, setError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string }>({});
  const [items, setItems] = useState<PhotoItem[]>([]);
  const [nextId, setNextId] = useState(1);
  const [selectedAction, setSelectedAction] = useState<number | null>(null); // + (1) albo - (0)
  const [selectedItem, setSelectedItem] = useState<number | null>(null); // item na którym kliknięto + / -
  const { getPolygonsOnImage, isProcessing } = useGetPolygonsOnImage();
  const { fetchJson } = useFetchJson();

  const { deleteDocument } = useDeleteDocument();
  const { unpublishDocument } = useUnpublishDocument();
  const { publishDocument } = usePublishDocument();
  const { updateFashionItem, success: updateSuccess } = useUpdateFashionItem();
  const { uploadJsonFile, uploadImageFile } = useCreateTemporaryFile();

  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const validateFields = () => {
    const formData = {
      title,
      urlSlug,
      desc,
      photographer,
      fullName,
      date,
      city,
      mail,
    };

    const errors: { [key: string]: string } = {};

    // Walidacja wymaganych pól (wyłączając social media)
    for (const [key, value] of Object.entries(formData)) {
      if (value.trim() === "") {
        errors[key] = "This field is required";
      }
    }

    // Walidacja zdjęcia (tylko dla nowych elementów)
    if (!item?.id && !image && !photoUrl) {
      errors.image = "Photo is required";
    }

    // Walidacja e-maila
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (mail && !emailRegex.test(mail)) {
      errors.mail = "Please enter a valid email address";
    }
    if (items) {
      items.forEach((item) => {
        const requiredFields: (keyof PhotoItem)[] = [
          "productName",
          "displayedName",
          "brand",
          "link",
          "zIndex",
        ];
        var isError = false;

        requiredFields.forEach((field) => {
          if (!item[field]) {
            isError = true;
          }
        });

        if (!item.thumbnail && !item.thumbnailKey) {
          isError = true;
        }
        if (isError) {
          errors[`item${item.objectId}`] = "This item is not valid";
        }
      });
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const clearFieldError = (fieldName: string) => {
    if (fieldErrors[fieldName]) {
      setFieldErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const handleDelete = async () => {
    try {
      setIsDeleteModalOpen(false);
      if (item?.id) {
        await deleteDocument(item.id);
        queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
        navigate("/");
      }
    } catch (error) {
      console.error("Error deleting photo:", error);
    }
  };

  const handleOpenSidebar = (i: number) => setSidebarOpen(i);
  const handleCloseSidebar = () => setSidebarOpen(null);

  const updateItem = (updatedItem: PhotoItem) => {
    clearFieldError(`item${updatedItem.objectId}`);
    setItems((prev) =>
      prev.map((item) =>
        item.objectId === updatedItem.objectId ? updatedItem : item
      )
    );
  };
  const handleSwitchChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsPublished(event.target.checked);
    if (event.target.checked) {
      await publishDocument(item.id);
    } else {
      await unpublishDocument(item.id);
    }
    queryClient.invalidateQueries({ queryKey: ["umbracoMedia", item.id] });
  };

  const handleSave = async () => {
    if (!validateFields()) {
      return;
    }

    setIsLoading(true);
    try {
      let updatedData = {
        title: title,
        description: desc,
        photographer: photographer,
        date: date,
        city: city,
        other: other,
        facebook: fb,
        mail: mail,
        mediaKey: imageMediaKey,
      };
      if (item?.id) {
        const addItem = await Promise.all(
          items.map(async (item) => ({
            minatureImageKey: item.thumbnail
              ? await processThumbnail(item.thumbnail)
              : item.thumbnailKey,
            affiliateLink: item.link,
            productName: item.productName,
            displayedName: item.displayedName,
            brand: item.brand,
            zIndex: item.zIndex,
          }))
        );

        const data = {
          ...updatedData,
          addItem,
        };
        let json = items.map((item) => ({
          displayedName: item.displayedName,
          productName: item.productName,
          brand: item.brand,
          link: item.link,
          polygons: item.polygons,
          selections: [...item.selections, ...item.newSelections],
        }));

        const temporaryFileId = await uploadJsonFile(json);
        await updateFashionItem(item.id, json, temporaryFileId, data);
        await publishDocument(item.id);
      } else {
        if (image) {
          var resp = await createAndUpload({ file: image, name: title });
          if (resp.documentKey && resp.mediaKey) {
            updatedData.mediaKey = resp.mediaKey;
            await updateFashionItem(resp.documentKey, null, null, updatedData);
            await publishDocument(resp.documentKey);
            navigate("/");
          } else {
            setError(true);
          }
        }
      }

      setIsSaveModalOpen(true);
      setIsLoading(false);
      queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
      queryClient.invalidateQueries({ queryKey: ["getUmbracoContent"] });
    } catch (error) {
      setIsLoading(false);
      console.error("Error updating photo item:", error);
    }
  };

  const processThumbnail = async (thumbnail: File) => {
    if (!thumbnail) return "";

    const temporaryFileId = await uploadImageFile(thumbnail);
    return temporaryFileId;
  };

  const addNewItem = () => {
    const photo = createPhotoItem({
      displayedName: `Object ${nextId}`,
      objectId: nextId,
    });
    setItems((prev) => [...prev, photo]);
    setNextId((prev) => prev + 1);
  };

  const deleteItem = (id: number) => {
    setItems((prev) => prev.filter((item) => item.objectId !== id));
  };

  const handleToggleButton = (id: number, action: number) => {
    if (isProcessing) return;

    if (selectedItem === id && selectedAction === action) {
      setSelectedAction(null);
      setSelectedItem(null);
    } else {
      setSelectedItem(id);
      setSelectedAction(action);
    }
  };

  const handleImageClick = async (x: number, y: number) => {
    if (isProcessing) return;
    if (selectedItem === null || selectedAction === null) return;

    const newSelection: PhotoSelection = {
      label: selectedAction,
      point: { x, y },
    };

    const selectedItemObj = items.find(
      (item) => item.objectId === selectedItem
    );
    if (!selectedItemObj) {
      console.warn("Selected item not found in items");
      return;
    }

    const allSelections = [
      ...selectedItemObj.selections,
      ...selectedItemObj.newSelections,
      newSelection,
    ];

    setItems((prev) =>
      prev.map((item) =>
        item.objectId === selectedItem
          ? { ...item, newSelections: [...item.newSelections, newSelection] }
          : item
      )
    );
    console.log(selectedItemObj);
    console.log(allSelections);

    const responseData = await getPolygonsOnImage({
      imageUrl: photoUrl,
      selections: allSelections,
    });

    setItems((prev) =>
      prev.map((item) =>
        item.objectId === selectedItem
          ? { ...item, polygons: responseData }
          : item
      )
    );
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragging(false);

    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith("image/")) {
      setImage(file);
      clearFieldError("image");
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      setImage(file);
      clearFieldError("image");
    }
  };

  return (
    <>
      {isLoading && <Loader />}
      <div className={styles.fashionComponent}>
        <div className={styles.header}>
          {item?.id ? (
            <div>
              <div
                className={`${styles.button}`}
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <DeleteIcon /> <p>Delete</p>
              </div>
              <div
                className={`${styles.button}`}
                onClick={() =>
                  window.open(
                    "https://traffique.astroid.com.pl" + item.route.path,
                    "_blank"
                  )
                }
              >
                <RemoveRedEyeIcon /> <p>Preview</p>
              </div>
            </div>
          ) : (
            <div></div>
          )}

          <div>
            {item?.id ? (
              <PublishSwitch
                handleSwitchChange={handleSwitchChange}
                isPublished={isPublished}
              />
            ) : (
              <div></div>
            )}
            <div
              className={`${styles.button} ${styles.save}`}
              onClick={handleSave}
            >
              <DoneIcon /> <p>Save</p>
            </div>
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.containerWithImage}>
            <div className={styles.leftSide}>
              {photoUrl ? (
                <CanvasOverImage
                  imageUrl={photoUrl}
                  items={items}
                  onImageClick={(x, y) => handleImageClick(x, y)}
                />
              ) : image != null ? (
                <img src={URL.createObjectURL(image)} alt="Fashion" />
              ) : (
                <div
                  className={`upload-container ${dragging ? "dragging" : ""} ${
                    fieldErrors.image ? styles.imageError : ""
                  } ${styles.uploadContainer}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <img
                    className={styles.uploadIcon}
                    src={addPhotoIcon}
                    alt="Upload photo"
                  />
                  <label className={styles.fileUpload}>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                    />
                    Drag photo here or upload a file
                  </label>
                  {fieldErrors.image && (
                    <Typography
                      variant="caption"
                      color="error"
                      sx={{ mt: 1, display: "block", textAlign: "center" }}
                    >
                      {fieldErrors.image}
                    </Typography>
                  )}
                </div>
              )}
            </div>
            <div className={styles.rightSide}>
              <Typography variant="body1">Title</Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  clearFieldError("title");
                }}
                error={!!fieldErrors.title}
                helperText={fieldErrors.title}
              />
              <Typography variant="body1">URL Slug</Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={urlSlug}
                onChange={(e) => {
                  setUrlSlug(e.target.value);
                  clearFieldError("urlSlug");
                }}
                error={!!fieldErrors.urlSlug}
                helperText={fieldErrors.urlSlug}
              />
              <Typography variant="body1">Description</Typography>
              <TextareaAutosize
                className={fieldErrors.desc ? styles.textareaError : ""}
                minRows={6}
                value={desc}
                onChange={(e) => {
                  setDesc(e.target.value);
                  clearFieldError("desc");
                }}
              />
              {fieldErrors.desc && (
                <Typography
                  variant="caption"
                  color="error"
                  sx={{ mt: 0.5, mb: 1, display: "block" }}
                >
                  {fieldErrors.desc}
                </Typography>
              )}
              <Typography variant="body1" sx={{ mt: fieldErrors.desc ? 0 : 2 }}>
                Photographer
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={photographer}
                onChange={(e) => {
                  setPhotographer(e.target.value);
                  clearFieldError("photographer");
                }}
                error={!!fieldErrors.photographer}
                helperText={fieldErrors.photographer}
              />
            </div>
          </div>
          <div className={styles.bottom}>
            <TextField
              placeholder="Full Name"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={fullName}
              onChange={(e) => {
                setFullName(e.target.value);
                clearFieldError("fullName");
              }}
              error={!!fieldErrors.fullName}
              helperText={fieldErrors.fullName}
            />
            <TextField
              placeholder="Date"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={date}
              onChange={(e) => {
                setDate(e.target.value);
                clearFieldError("date");
              }}
              error={!!fieldErrors.date}
              helperText={fieldErrors.date}
            />
            <TextField
              placeholder="City"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={city}
              onChange={(e) => {
                setCity(e.target.value);
                clearFieldError("city");
              }}
              error={!!fieldErrors.city}
              helperText={fieldErrors.city}
            />
            <TextField
              placeholder="Mail"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={mail}
              onChange={(e) => {
                setMail(e.target.value);
                clearFieldError("mail");
              }}
              error={!!fieldErrors.mail}
              helperText={fieldErrors.mail}
            />
            <TextField
              placeholder="@IG"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={ig}
              onChange={(e) => setIg(e.target.value)}
            />
            <TextField
              placeholder="@FB"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={fb}
              onChange={(e) => setFb(e.target.value)}
            />
            <TextField
              placeholder="@TIKTOK"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={tiktok}
              onChange={(e) => setTiktok(e.target.value)}
            />
            <TextField
              placeholder="@Other"
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={other}
              onChange={(e) => setOther(e.target.value)}
            />
          </div>
          {item?.id ? (
            <div className={styles.itemsConteiner}>
              <div className={styles.itemContainerHeader}>
                <p className={styles.title}>ITEMS</p>
                <p
                  className={styles.clearButton}
                  onClick={() => {
                    setItems([]);
                    setNextId(1);
                  }}
                >
                  Clear all
                </p>
              </div>
              {items.map((item, index) => {
                return (
                  <div
                    className={`${styles.item} ${
                      fieldErrors[`item${item.objectId}`]
                        ? styles.itemError
                        : ""
                    }`}
                    key={index}
                  >
                    <div className={styles.leftSide}>
                      <p>{item.displayedName}</p>
                      {fieldErrors[`item${item.objectId}`] && (
                        <p className={styles.error}>
                          {fieldErrors[`item${item.objectId}`]}
                        </p>
                      )}
                    </div>
                    <div className={styles.rightSide}>
                      <Button
                        className={
                          styles.toggleButton +
                          (selectedItem === item.objectId &&
                          selectedAction === 1
                            ? ` ${styles.clickedButton}`
                            : "")
                        }
                        disabled={isProcessing}
                        onClick={() => {
                          handleToggleButton(item.objectId, 1);
                        }}
                      >
                        <AddIcon />
                      </Button>
                      <Button
                        className={
                          styles.toggleButton +
                          (selectedItem === item.objectId &&
                          selectedAction === 0
                            ? ` ${styles.clickedButton}`
                            : "")
                        }
                        disabled={isProcessing}
                        onClick={() => {
                          handleToggleButton(item.objectId, 0);
                        }}
                      >
                        <RemoveIcon />
                      </Button>
                      <Button onClick={() => handleOpenSidebar(index)}>
                        <EditIcon />
                      </Button>
                      <Button
                        onClick={() => {
                          deleteItem(item.objectId);
                        }}
                      >
                        <DeleteIcon />
                      </Button>
                    </div>
                  </div>
                );
              })}

              <div className={styles.addItem} onClick={addNewItem}>
                Add Item
              </div>
            </div>
          ) : (
            <div></div>
          )}
        </div>
      </div>
      <SaveConfirmationModal
        open={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        success={updateSuccess || !error}
      />
      <DeleteConfirmationModal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
      />
      <SidebarForm
        open={isSidebarOpen !== null}
        onClose={handleCloseSidebar}
        item={items[isSidebarOpen || 0]}
        handleSave={(updatedItem) => updateItem(updatedItem)}
      />
    </>
  );
};

export default FashionComponent;
