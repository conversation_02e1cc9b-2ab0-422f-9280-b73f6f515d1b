import { Check, Delete, Visibility } from '@mui/icons-material';
import styles from './productsComponent.module.scss'
import {
    Box,
    Grid,
    TextField,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Button,
    Paper,
    TextareaAutosize,
} from "@mui/material";

const ProductsComponent = ({ item }: any) => {
    const {
        properties: { description, image, photographerName, websiteLink, cities: items },
    } = item;
    return (
        <>
            <div className={styles.fashionComponent}>
                <div className={styles.buttonsContainer}>
                    <div>
                        <Button
                            variant="contained"
                            startIcon={<Delete />}
                            sx={{
                                backgroundColor: '#FF1D26',
                                color: 'white',
                                textTransform: 'none',
                                '&:hover': {
                                    backgroundColor: '#c82333'
                                },
                                fontWeight: 500,
                                px: 3
                            }}
                        >
                            Delete
                        </Button>

                        <Button
                            variant="contained"
                            startIcon={
                                <Visibility
                                    sx={{
                                        color: '#000000'
                                    }}
                                />
                            }
                            sx={{
                                backgroundColor: '#F2F2F2',
                                color: 'white',
                                textTransform: 'none',
                                '&:hover': {
                                    backgroundColor: '#dde2e6'
                                },
                                fontWeight: 500,
                                px: 3,
                                ml: 2,
                            }}
                        >
                            Preview
                        </Button>
                    </div>

                    <Button
                        variant="contained"
                        startIcon={<Check />}
                        sx={{
                            backgroundColor: '#79C942',
                            color: 'white',
                            textTransform: 'none',
                            '&:hover': {
                                backgroundColor: '#218838'
                            },
                            fontWeight: 500,
                            px: 3
                        }}
                    >
                        Save
                    </Button>
                </div>
                <div className={styles.optionsContainer}>
                    <div className={styles.switchesContainer}>
                        <div className={styles.group}>
                            <span className={styles.label}>PUBLISHED</span>
                            <div className={styles.group}>
                                <label className={styles.switch}>
                                    <input
                                        type="checkbox"
                                        defaultChecked
                                        className={styles.switchInput}
                                    />
                                    <span className={styles.switchSlider}></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <Grid container sx={{
                    backgroundColor: '#F5F5F5',
                    padding: '50px 20px 20px 20px'
                }}>
                    <Grid item xs={12} md={4}>
                        <Box
                            component="img"
                            src={'https://duetprodumbraco.azurewebsites.net/' + image[0].url}
                            alt="Fashion"
                            sx={{
                                width: "100%",
                                borderRadius: "8px",
                                border: "1px solid #ddd",
                                objectFit: "cover",
                                marginBottom: '20px',
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} md={8} pl={3}>
                        <Box component="form" sx={{ mb: 4 }}>
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Name
                            </Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                sx={{ mb: 2 }}
                                defaultValue={photographerName}
                            />
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                URL Slug
                            </Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                sx={{ mb: 2 }}
                                defaultValue={item.route.path}
                            />
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Description
                            </Typography>
                            <TextareaAutosize
                                minRows={6}
                                style={{
                                    width: "100%",
                                    padding: "16px",
                                    borderRadius: "4px",
                                    border: "1px solid #ccc",
                                    backgroundColor: 'transparent',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '16px'
                                }}
                                defaultValue={description}
                            />
                            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                Website
                            </Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                sx={{ mb: 2 }}
                                defaultValue={item.properties.websiteLink}
                            />
                        </Box>
                    </Grid>
                    <TableContainer component={Paper} sx={{
                        backgroundColor: 'transparent',
                        maxWidth: '400px'
                    }}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>City</TableCell>
                                    <TableCell>Actions</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {item.properties.cities.items.map((item: any, index: number) => (
                                    <TableRow
                                        key={index}
                                        sx={{
                                            cursor: 'pointer',
                                            '&:hover': {
                                                backgroundColor: '#fff',
                                            },
                                            transition: 'background-color 0.2s ease',
                                        }}
                                        onClick={() => console.log(`Row ${index + 1} clicked`)}
                                    >
                                        <TableCell>{item.content.properties.cityname}</TableCell>
                                        <TableCell>-</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
            </div>
        </>
    );
};

export default ProductsComponent;
