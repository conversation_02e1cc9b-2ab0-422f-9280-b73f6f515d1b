import { useQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS } from '../constants/urls';

const fetchHowItWorks = async () => {
  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/delivery/api/v2/content?filter=contentType:how&take=1000000`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch shop page data');
  }

  const data = await response.json();

  return data.items;
};

const useGetHowItWorksData = () => {
  return useQuery({
    queryKey: ['how'],
    queryFn: fetchHowItWorks,
  });
};

export default useGetHowItWorksData;
