import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}/umbraco/management/api/v1/document`;

const useUpdateAboutUsItem = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updateAboutUsItem = async (id: string, data: { heading: string; paragraph: string; heading2: string; cities: string[]; photographers: string[] }) => {
        setLoading(true);
        setError(null);
        setSuccess(false);
        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const requestBody = {
            values: [
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "heading",
                    culture: null,
                    segment: null,
                    value: data.heading
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "paragraph",
                    culture: null,
                    segment: null,
                    value: data.paragraph
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "secondaryHeading",
                    culture: null,
                    segment: null,
                    value: data.heading2
                },
                {
                    editorAlias: "Umbraco.BlockList",
                    alias: "cities",
                    culture: null,
                    segment: null,
                    value: {
                        layout: {
                            "Umbraco.BlockList": data.cities.map(() => ({
                                $type: "BlockListLayoutItem",
                                contentUdi: null,
                                settingsUdi: null,
                                contentKey: "572d2433-2bea-47a5-8d06-79748678d781",
                                settingsKey: null
                            }))
                        },
                        contentData: data.cities.map(city => ({
                            contentTypeKey: "2cc7ccee-bac4-4651-99f8-359cafe09f3d",
                            udi: null,
                            key: "572d2433-2bea-47a5-8d06-79748678d781",
                            values: [{
                                editorAlias: "Umbraco.TextBox",
                                alias: "cityname",
                                culture: null,
                                segment: null,
                                value: city
                            }]
                        })),
                        settingsData: []
                    }
                },
                {
                    editorAlias: "Umbraco.BlockList",
                    alias: "photographers",
                    culture: null,
                    segment: null,
                    value: {
                        layout: {
                            "Umbraco.BlockList": data.photographers.map(() => ({
                                $type: "BlockListLayoutItem",
                                contentUdi: null,
                                settingsUdi: null,
                                contentKey: "572d2433-2bea-47a5-8d06-79748678d781",
                                settingsKey: null
                            }))
                        },
                        contentData: data.photographers.map(photographer => ({
                            contentTypeKey: "2cc7ccee-bac4-4651-99f8-359cafe09f3d",
                            udi: null,
                            key: "572d2433-2bea-47a5-8d06-79748678d781",
                            values: [{
                                editorAlias: "Umbraco.TextBox",
                                alias: "photographerName",
                                culture: null,
                                segment: null,
                                value: photographer
                            }]
                        })),
                        settingsData: []
                    }
                }
            ],
            variants: [
                {
                    culture: null,
                    segment: null,
                    state: "Published",
                    name: "About Us - Traffique"
                }
            ],
            template: {
                id: "53c6c887-ff4b-4c3f-ab05-5a079a40a53f"
            }
        };

        try {
            let response = await fetch(`${API_URL}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${currentToken}`
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `Bearer ${currentToken}`
                        },
                        body: JSON.stringify(requestBody)
                    });
                }
                if (!response.ok) {
                    throw new Error(`Błąd: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updateAboutUsItem, loading, error, success };
};

export default useUpdateAboutUsItem;
