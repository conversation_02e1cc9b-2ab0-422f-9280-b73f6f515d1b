import LabeledTextField from '../LabeledTextField/LabeledTextField'
import styles from '../movieEditorItemList/movieEditorItemList.module.scss';
import {FormItem} from "../../types/formItem";
import React, {useEffect, useState} from "react";

interface Props {
    item: FormItem,
    sendUpdate: (item: FormItem) => void;
}


const MovieEditorItemForm: React.FC<Props> = ({ item, sendUpdate}: Props) => {
    const [localItem, setLocalItem] = useState<FormItem>(item);

    useEffect(() => {
        setLocalItem({ ...item });
    }, [item, item.itemName, item.brand, item.link]);

    const handleChange = (field: keyof FormItem, value: string) => {
        setLocalItem((prev) => ({ ...prev, [field]: value }));
    };

    const handleBlur = () => {
        sendUpdate(localItem);
    };

    return (
        <div className={styles.rightArea}>
            <LabeledTextField
                label="Item"
                value={localItem.itemName}
                onChange={(val) => handleChange('itemName', val)}
                onBlur={handleBlur}
            />
            <LabeledTextField
                label="Brand"
                value={localItem.brand}
                onChange={(val) => handleChange('brand', val)}
                sx={{mt: 3}}
                onBlur={handleBlur}
            />
            <LabeledTextField
                label="Link"
                value={localItem.link}
                onChange={(val) => handleChange('link', val)}
                sx={{mt: 3}}
                onBlur={handleBlur}
            />
        </div>
    )
};

export default MovieEditorItemForm;