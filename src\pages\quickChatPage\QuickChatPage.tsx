import { useLocation } from "react-router-dom";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from './quickChatPage.module.scss';
import useGetHowItWorksData from "../../api/useGetHowItWorksData";
import { Grid, Box, Typography, TextField, MenuItem, Button, TableCell } from "@mui/material";
import { useState } from "react";
import TextareaAutosize from "@mui/material/TextareaAutosize";
import { Check, Delete, Visibility } from "@mui/icons-material";
import useGetQuickChatData from "../../api/useGetQuickChatData";
import Loader from "../../components/loader/Loader";
import useUpdateQuickItem from "../../api/useUpdateQuickItem";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DoneIcon from '@mui/icons-material/Done';
import { CLIENTAPP_FRONTEND_ADDRESS } from "../../constants/urls";
import SaveConfirmationModal from '../../components/saveConfirmationModal/SaveConfirmationModal';

const QuickChatPage = () => {
    const { data, isLoading, error } = useGetQuickChatData();
    const [fields, setFields] = useState<Record<string, string>>({});
    const { updateQuickItem } = useUpdateQuickItem();
    const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);

    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading data" />;


    const handleFieldChange = (alias: string, value: string) => {
        setFields(prev => ({ ...prev, [alias]: value }));
    };

    const handleSave = async () => {
        const id = '35ffe33e-b320-4080-9ff6-d33e69a9d635';
        try {
            await updateQuickItem(id, {
                heading: fields.heading || "",
                paragraph: fields.paragraph || "",
                paragraph2: fields.paragraph2 || "",
                paragraph3: fields.paragraph3 || "",
                paragraph4: fields.paragraph4 || "",
                paragraph5: fields.paragraph5 || "",
                paragraph6: fields.paragraph5 || "",
                paragraph7: fields.paragraph5 || "",
            });
            setIsSaveModalOpen(true);
        } catch (error) {
            console.error("Error updating about us item:", error);
        }
    };

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <div style={{ width: '100%' }}>
                    <div className={styles.header}>
                        <div>
                            <div className={`${styles.button}`} onClick={() => window.open(CLIENTAPP_FRONTEND_ADDRESS + 'quick-chats', "_blank")}>
                                <RemoveRedEyeIcon /> <p>Preview</p>
                            </div>
                        </div>
                        <div>
                            <div className={`${styles.button} ${styles.save}`} onClick={handleSave}>
                                <DoneIcon /> <p>Save</p>
                            </div>
                        </div>
                    </div>
                    {data && data.map((item: any) => (
                        <Grid container spacing={2} key={item.id} sx={{ mb: 4 }}>
                            <Grid item xs={12} md={12} pl={12}>
                                <Box component="form" sx={{ mb: 4 }}>
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Name
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.name}
                                    />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        URL Slug
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.route.path}
                                    />
                                    <EditableField label="Heading" alias="heading" markup={item.properties?.heading?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph" alias="paragraph1" markup={item.properties?.paragraph1?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image && item.properties.image.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[0].url}`}
                                            alt={item.properties.image[0].name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph2" markup={item.properties?.paragraph2?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image && item.properties.image.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph3" markup={item.properties?.paragraph3?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image2 && item.properties.image2.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph4" markup={item.properties?.paragraph4?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image3 && item.properties.image3.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph5" markup={item.properties?.paragraph5?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image4 && item.properties.image4.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph6" markup={item.properties?.paragraph6?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image5 && item.properties.image5.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                    <EditableField label="Paragraph" alias="paragraph7" markup={item.properties?.paragraph7?.markup} onChange={handleFieldChange} />
                                    {item.properties?.image6 && item.properties.image6.length > 0 && (
                                        <Box
                                            component="img"
                                            src={`https://admintraffique.astroid.com.pl${item.properties.image[1]?.url}`}
                                            alt={item.properties.image[1]?.name || 'Image'}
                                            sx={{
                                                height: 240,
                                                borderRadius: "4px",
                                                objectFit: "cover",
                                                mb: 2,
                                            }}
                                        />
                                    )}
                                </Box>
                            </Grid>
                        </Grid>
                    ))}
                </div>
                <SaveConfirmationModal
                    open={isSaveModalOpen}
                    onClose={() => setIsSaveModalOpen(false)}
                />
            </div>
        </div>
    );
};

const EditableField = ({ label, alias, markup, onChange }: { label: string; alias: string; markup: string | null; onChange: (alias: string, value: string) => void; }) => {
    const [value, setValue] = useState(markup?.replace(/<[^>]*>/g, "") || "");

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
        setValue(e.target.value);
        onChange(alias, e.target.value);
    };

    return (
        <div style={{ marginBottom: "16px" }}>
            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>{label}</Typography>
            <TextField fullWidth variant="outlined" value={value} onChange={handleChange} />
        </div>
    );
};

export default QuickChatPage;
