import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}/umbraco/management/api/v1/document`;

const useUpdatePhotographerItem = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const mapCitiesToContentData = (cities: string[]) => {
        return cities.map((city) => ({
            contentTypeKey: "2cc7ccee-bac4-4651-99f8-359cafe09f3d",
            udi: null,
            key: "572d2433-2bea-47a5-8d06-79748678d781",
            values: [
                {
                    editorAlias: "Umbraco.TextBox",
                    culture: null,
                    segment: null,
                    alias: "cityname",
                    value: city,
                },
            ],
        }));
    };

    //<PERSON><PERSON>ić kod później, p<PERSON><PERSON><PERSON><PERSON><PERSON> powtórzenia do funkcji, stworzy<PERSON> typ dla image
    const updatePhotographerItem = async (id: string, data: { title: string; description: string; photographer: string; cities: string[]; image: any }) => {
        setLoading(true);
        setError(null);
        setSuccess(false);
        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const requestBody = {
            "values": [
                {
                    "editorAlias": "Umbraco.TextBox",
                    "alias": "photographerName",
                    "culture": null,
                    "segment": null,
                    "value": `${data.title}`
                },
                {
                    "editorAlias": "Umbraco.TextBox",
                    "alias": "description",
                    "culture": null,
                    "segment": null,
                    "value": `${data.description}`
                },
                {
                    "editorAlias": "Umbraco.BlockList",
                    "alias": "cities",
                    "culture": null,
                    "segment": null,
                    "value": {
                        "layout": {
                            "Umbraco.BlockList": data.cities.map(() => ({
                                "$type": "BlockListLayoutItem",
                                "contentUdi": null,
                                "settingsUdi": null,
                                "contentKey": "572d2433-2bea-47a5-8d06-79748678d781",
                                "settingsKey": null
                            }))
                        },
                        "contentData": mapCitiesToContentData(data.cities),
                        "settingsData": [],
                        "expose": data.cities.map(() => ({
                            "contentKey": "572d2433-2bea-47a5-8d06-79748678d781",
                            "culture": null,
                            "segment": null
                        }))
                    }
                },
                {
                    "editorAlias": "Umbraco.TextBox",
                    "alias": "websiteLink",
                    "culture": null,
                    "segment": null,
                    "value": `${data.photographer}`
                },
                {
                    "editorAlias": "Umbraco.MediaPicker3",
                    "alias": "image",
                    "culture": null,
                    "segment": null,
                    "value": `[{"crops":[],"focalPoint":null,"mediaKey":"${data.image[0].id}","mediaTypeAlias":"Image"}]`
                }
            ],
            "variants": [
                {
                    "culture": null,
                    "segment": null,
                    "state": "Published",
                    "name": `${data.title}`
                }
            ],
            "template": {
                "id": "2be54dc6-bd71-4b4e-a65f-40fae98faf7c"
            }
        };

        try {
            let response = await fetch(`${API_URL}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify(requestBody),
            });
            let response2 = await fetch(`${API_URL}/${id}/publish`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify({
                    "publishSchedules": [
                        {
                            "culture": null,
                        }
                    ]
                }),
            });

            if (!response.ok || !response2.ok) {
                if (response.status === 401 || response2.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    });
                    response2 = await fetch(`${API_URL}/${id}/publish`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify({
                            "publishSchedules": [
                                {
                                    "culture": null,
                                }
                            ]
                        }),
                    });
                }
                if (!response.ok) {
                    throw new Error(`Błąd: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updatePhotographerItem, loading, error, success };
};

export default useUpdatePhotographerItem;
