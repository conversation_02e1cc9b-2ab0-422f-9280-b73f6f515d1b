@import '../../styles/variables';

.fashionComponent {
    width: 100%;
    padding: 0 24px;

    .header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 56px 0;

        div {
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:nth-last-child(1) {
                width: 100%;
                display: flex;
                justify-content: end;
            }

            .button {
                font-size: 20px;
                letter-spacing: 1px;
                font-weight: 600;
                text-align: center;
                padding: 12px 40px;
                margin-right: 10px;
                color: $white;
                cursor: pointer;
                transition: 0.3s ease;
                background-color: $darkGray;

                &:last-of-type {
                    margin-right: 0;
                }

                &:hover {
                    filter: brightness(1.2);
                }

                p {
                    margin-left: 10px;
                }
            }

            .save {
                background-color: $green;
                width: auto;
            }
        }
    }

    .optionsContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 20px;

        .button {
            color: $darkGray;
            font-size: 18px;
            background: none;
            border: none;
            cursor: pointer;

            &:hover {
                color: #333;
            }
        }

        .switchesContainer {
            display: flex;
            align-items: center;

            .group:nth-of-type(2) {
                margin-left: 20px;
            }
        }

        .group {
            display: flex;
            align-items: center;
            gap: 8px;

            .label {
                font-weight: 500;
                color: $darkGray;
            }

            .switch {
                position: relative;
                display: inline-flex;
                align-items: center;
                width: 44px;
                height: 24px;
                padding: 2px;

                .switchInput {
                    opacity: 0;
                    width: 0;
                    height: 0;
                    position: absolute;

                    &:checked+.switchSlider {
                        background-color: #3b82f6;

                        &:before {
                            transform: translateX(20px);
                        }
                    }
                }

                .switchSlider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: #ccc;
                    transition: 0.2s;
                    border-radius: 24px;

                    &:before {
                        position: absolute;
                        content: "";
                        height: 20px;
                        width: 20px;
                        left: 2px;
                        bottom: 2px;
                        background-color: white;
                        transition: 0.2s;
                        border-radius: 50%;
                    }
                }
            }

            .input {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 12px;
                width: 160px;

                &:focus {
                    outline: none;
                    border-color: #999;
                }
            }

        }
    }
}