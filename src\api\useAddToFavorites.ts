import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/api/Favorite`;

const useAddToFavorite = () => {
  const { token, fetchToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const addToFavorite = async (id: String) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    if (!token) {
      await fetchToken();
      return;
    }

    try {
      const response = await fetch(`${API_URL}?id=${id}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          await fetchToken();
        }
        throw new Error(`Error: ${response.statusText}`);
      }

      setSuccess(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { addToFavorite, loading, error, success };
};

export default useAddToFavorite;
