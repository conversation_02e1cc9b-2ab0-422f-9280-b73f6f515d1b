import { useState } from "react";
import { HOST_ADRESS } from "../constants/urls";
import {getConnectionUrl} from "../websockets/wsClient";

interface SaveVideoData {
  type: string;
  objects: {
    objectId: number;
    url: string;
    brand: string;
    idName: string;
    name: string;
    image: File | string | null;
  }[];
}

interface SaveVideoResponse {
  success?: boolean;
  message?: string;
  data?: any;
}

export function useSaveVideo() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const saveVideo = async (
    sessionId: string | null, 
    data: SaveVideoData
  ): Promise<SaveVideoResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${getConnectionUrl()}/api/download_result`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Session-Id": sessionId || "",
        },
        //body: JSON.stringify(data),
      });

      if (!response.ok) {
        switch (response.status) {
          case 401:
            throw new Error("Unauthorized (401)");
          case 422: {
            const errorData = await response.json();
            const msg = errorData?.detail?.msg || "Validation error (422)";
            throw new Error(msg);
          }
          case 503:
            throw new Error("Session not running (503)");
          default:
            throw new Error(`Request failed with status ${response.status}`);
        }
      }

      const responseData: SaveVideoResponse = await response.json();
      return responseData;
    } catch (err) {
      const errorObj = err as Error;
      setError(errorObj.message);
      throw errorObj;
    } finally {
      setIsLoading(false);
    }
  };

  return { saveVideo, isLoading, error };
}
