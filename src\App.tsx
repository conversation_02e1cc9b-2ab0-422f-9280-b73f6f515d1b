import { Box } from "@mui/material";
import "./styles/globales.scss";
import theme from "./styles/theme";
import { ThemeProvider } from "@emotion/react";
import AppRoutes from "./routes";

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <div className="App">
        <Box sx={{ minHeight: "100vh" }}>
          <AppRoutes />
        </Box>
      </div>
    </ThemeProvider>
  );
};

export default App;
