import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./modelsGrid.module.scss";
import useGetShopPageData from "../../api/useGetShopPageData";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import MessageAlert from "../messageAlert/MessageAlert";

const ModelsGrid = () => {
  const { data, isLoading, error } = useGetShopPageData();
  const [activeSort, setActiveSort] = useState<string>("title-asc");
  const [visibleCount, setVisibleCount] = useState<number>(9);
  const navigate = useNavigate();

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  if (isLoading) return <p>Loading...</p>;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  // Pobieramy wszystkie modele z danych (z właściwości socials)
  const allModels: string[] = (data?.flatMap((item: any) => item.properties.socials) ?? [])
    .filter((model: any): model is string => typeof model === "string");

  // Usuwamy duplikaty
  const uniqueModels: string[] = Array.from(new Set(allModels));

  // Sortowanie modeli
  let sortedModels = [...uniqueModels];
  if (activeSort === "title-asc") {
    sortedModels.sort((a: string, b: string) => a.localeCompare(b));
  } else if (activeSort === "title-desc") {
    sortedModels.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "newest") {
    // Przykładowe sortowanie – przy braku dat sortujemy alfabetycznie w odwrotnej kolejności
    sortedModels.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "oldest") {
    sortedModels.sort((a: string, b: string) => a.localeCompare(b));
  }

  // Paginacja – pobieramy tylko tyle modeli, ile określa visibleCount
  const paginatedModels = sortedModels.slice(0, visibleCount);

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 3);
  };

  return (
    <div className={styles.photoGrid}>
      <div className={styles.sortOptions}>
        <p>Sort by:</p>
        {["title-asc", "title-desc", "newest", "oldest"].map((sortType) => (
          <span
            key={sortType}
            className={`${styles.sortOption} ${activeSort === sortType ? styles.active : ""}`}
            onClick={() => handleSortChange(sortType)}
          >
            {sortType === "title-asc"
              ? "Title (A > Z)"
              : sortType === "title-desc"
                ? "Title (Z > A)"
                : sortType.charAt(0).toUpperCase() + sortType.slice(1)}
          </span>
        ))}
      </div>
      <div className={styles.mainContent}>
        <TableContainer component={Paper} sx={{ backgroundColor: "transparent" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Model</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedModels.map((model: string, index: number) => (
                <TableRow
                  key={index}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { backgroundColor: "#fff" },
                    transition: "background-color 0.2s ease",
                  }}
                  onClick={() => navigate(`/models/${encodeURIComponent(model)}`)}
                >
                  <TableCell>{model}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {visibleCount < sortedModels.length && (
          <div className={styles.loadMore} onClick={handleLoadMore}>
            <p>MORE</p>
            <KeyboardArrowDownIcon />
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelsGrid;
