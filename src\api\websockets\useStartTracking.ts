import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useStartTracking() {
  const [isLoadingStartTracking, setIsLoading] = useState(false);
  const [errorStartTracking, setError] = useState<Error | null>(null);

  const sendStartTracking = async (frameIdx: number) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.startTracking,
          {
            "start_frame_index": frameIdx,
            "polygon": true
          }
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendStartTracking, isLoadingStartTracking, errorStartTracking };
}
