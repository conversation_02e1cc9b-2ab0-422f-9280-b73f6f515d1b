import { useLocation } from "react-router-dom";
import { <PERSON> } from "react-router-dom";
import SidebarItem from "./sideBarItem/sideBarItem";
import styles from "./sidebar.module.scss";

interface SidebarProps {
  isClique?: boolean;
}

const Sidebar = ({ isClique = false }: SidebarProps) => {
  const menuItems = [
    { title: "Photos", count: null, path: "/" },
    { title: "Faves", count: null, path: "/faves" },
    { title: "Photographers", count: null, path: "/photographers" },
    { title: "Brands", count: null, path: "/brands" },
    { title: "Products", count: null, path: "/products" },
    { title: "Models", count: null, path: "/models" },
    { title: "Home", count: null, path: "/home" },
    { title: "About", count: null, path: "/about-us" },
    { title: "How it Works", count: null, path: "/how-it-works" },
    { title: "Quick Chat", count: null, path: "/quick-chat" },
    { title: "Privacy Policy", count: null, path: "/privacy-policy" },
    { title: "Clique", count: null, path: "/clique" },
    { title: "Clique Brands", path: "/movieBrands" },
    { title: "Clique Products", path: "/movieProducts" },
    { title: "Data", count: null, path: "/charts" },
    { title: "Form", count: null, path: "/form" },
  ];

  const location = useLocation();
  const currentPath = location.pathname;

  const isPathActive = (itemPath: string) => {
    if (itemPath === "/") {
      return currentPath === "/";
    }

    const itemPathWord = itemPath.substring(1).split("/")[0].toLowerCase();
    const currentPathWord = currentPath
      .substring(1)
      .split("/")[0]
      .toLowerCase();

    if (itemPathWord && currentPathWord) {
      return (
        currentPathWord.startsWith(itemPathWord) ||
        itemPathWord.startsWith(currentPathWord) ||
        currentPath === itemPath ||
        currentPath.startsWith(`${itemPath}/`)
      );
    }

    return false;
  };

  return (
    <div
      className={`${styles.sidebar} ${isClique ? styles.sidebarClique : ""}`}
    >
      {menuItems.map((item, index) => (
        <Link key={index} to={item.path || "#"} className={styles.sidebarLink}>
          <SidebarItem
            title={item.title}
            count={item.count}
            isActive={isPathActive(item.path)}
          />
        </Link>
      ))}
    </div>
  );
};

export default Sidebar;
