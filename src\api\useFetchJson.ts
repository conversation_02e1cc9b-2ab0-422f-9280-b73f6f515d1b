import { useState, useCallback } from "react";
import { UMBRACO_ADRESS } from "../constants/urls";

function useFetchJson() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchJson = useCallback(async (url: string): Promise<{}> => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${UMBRACO_ADRESS}${url}`);
      if (!response.ok) {
        throw new Error(`Błąd sieci: ${response.status}`);
      }

      var respJson = await response.json();
      return respJson;
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
      return {};
    } finally {
      setLoading(false);
    }
  }, []);

  return { fetchJson, loading, error };
}
export default useFetchJson;
