import React, { useState } from 'react';
import SearchIcon from '@mui/icons-material/Search';

const cities = ['Everywhere', 'LONDON', 'NEW YORK', 'BERLIN', 'PARIS', 'TOKYO'];

interface SearchProps {
    searchQuery: string;
    selectedCity: string;
    onSearch: (query: string) => void;
    onCityChange: (city: string) => void;
}

const Search: React.FC<SearchProps> = ({ searchQuery, selectedCity, onSearch, onCityChange }) => {
    const [keyword, setKeyword] = useState(searchQuery);

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            onSearch(keyword);
        }
    };

    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px', justifyContent: "center", marginBottom: "40px", fontFamily: 'Arial, sans-serif' }}>
            <SearchIcon sx={{ fontSize: '3.2rem', color: "black" }} />
            <input
                type="text"
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="#HIPHOP"
                style={{
                    padding: '33px 30px',
                    border: '1px solid #E0E0E0',
                    borderRadius: '2px',
                    fontSize: '16px',
                    color: '#333',
                    fontWeight: 'bold',
                    width: '60%',
                }}
            />
            <span style={{ fontSize: '20px', fontWeight: 'normal', color: '#333', marginLeft: '20px', marginRight: '20px' }}>IN</span>
            <select
                value={selectedCity}
                onChange={(e) => onCityChange(e.target.value)}
                style={{
                    padding: '30px 30px',
                    border: '4px solid grey',
                    borderRadius: '2px',
                    fontSize: '16px',
                    color: 'grey',
                    fontWeight: 'bold',
                    backgroundColor: '#EEEEEE',
                    cursor: 'pointer',
                    width: '20%'
                }}
            >
                {cities.map((city) => (
                    <option key={city} value={city}>
                        {city.toUpperCase()}
                    </option>
                ))}
            </select>
        </div>
    );
};

export default Search;
