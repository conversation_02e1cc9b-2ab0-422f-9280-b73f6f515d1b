import { useEffect, useState } from "react";
import useGoogleAuth from "./useGoogleAuth";

const PROPERTY_ID = "455433846";

const useExternalLinkClicks = () => {
  const { token } = useGoogleAuth();
  const [externalClicks, setExternalClicks] = useState<Record<string, number>>({});

  useEffect(() => {
    if (!token) return;

    const fetchAnalyticsData = async () => {
      const url = `https://analyticsdata.googleapis.com/v1beta/properties/${PROPERTY_ID}:runReport`;

      // Konfiguracja raportu:
      // - Pobieramy eventCount, czyli liczbę wystąpień zdarzeń.
      // - Używamy trzech wymiarów: pagePath (podstrona), linkUrl (kliknięty link) oraz outbound (czy link jest wychodzący).
      // - Filtrujemy wyniki, aby brać pod uwagę tylko eventy o nazwie "click"
      //   i tylko te, gdzie outbound = "true".
      const requestBody = {
        dateRanges: [{ startDate: "30daysAgo", endDate: "today" }],
        metrics: [{ name: "eventCount" }],
        dimensions: [
          { name: "pagePath" },    // Podstrona, na której nastąpiło kliknięcie
          { name: "linkUrl" },     // URL klikniętego linku
          { name: "outbound" }     // Czy link jest wychodzący ("true"/"false")
        ],
        dimensionFilter: {
          andGroup: {
            expressions: [
              {
                filter: {
                  fieldName: "eventName",
                  stringFilter: {
                    value: "click",
                    matchType: "EXACT",
                  },
                },
              },
              {
                filter: {
                  fieldName: "outbound",
                  stringFilter: {
                    value: "true",
                    matchType: "EXACT",
                  },
                },
              },
            ],
          },
        },
      };

      try {
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();

        if (!data.rows) {
          console.warn("⚠️ Brak danych z GA4");
          return;
        }

        // Agregacja kliknięć według podstrony (pagePath)
        const clicksByPage = data.rows.reduce((acc: Record<string, number>, row: any) => {
          const pagePath = row.dimensionValues[0]?.value || "";
          const clicks = parseInt(row.metricValues[0]?.value || "0", 10);

          // Usunięcie przedrostka "/shop-pages/" oraz ewentualnego końcowego "/"
          let cleanPath = pagePath.replace("/shop-pages/", "");
          cleanPath = cleanPath.replace(/\/$/, "");

          acc[cleanPath] = (acc[cleanPath] || 0) + clicks;
          return acc;
        }, {} as Record<string, number>);

        console.log("📊 Kliknięcia linków zewnętrznych według podstrony:", clicksByPage);
        setExternalClicks(clicksByPage);
      } catch (error) {
        console.error("❌ Błąd pobierania danych z GA4:", error);
      }
    };

    fetchAnalyticsData();
  }, [token]);

  return externalClicks;
};

export default useExternalLinkClicks;