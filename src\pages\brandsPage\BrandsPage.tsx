import { useLocation } from "react-router-dom";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from './brandsPage.module.scss'
import BrandsComponent from "../../components/brandsComponent/brandsComponent";

const BrandsPage = () => {
    const location = useLocation();
    const { item } = location.state || {};

    if (!item) {
        return <p>No data available</p>;
    }
    
    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <BrandsComponent item={item} />
            </div>
        </div>
    );
};

export default BrandsPage;
