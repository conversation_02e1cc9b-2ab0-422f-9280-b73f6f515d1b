import React, { useState, useEffect } from 'react';
import styles from './idleTimeoutPopup.module.scss';
import {Button} from "@mui/material";
import {useRefreshSession} from "../../api/websockets/useRefreshSession";

interface IdleTimeoutPopupProps {
  show: boolean;
  timeoutInSeconds: number;
}

const IdleTimeoutPopup: React.FC<IdleTimeoutPopupProps> = ({ show, timeoutInSeconds }) => {
  const [timeRemaining, setTimeRemaining] = useState(timeoutInSeconds);

  const { sendRefreshSession, isLoadingRefresh, errorRefresh } = useRefreshSession();

  useEffect(() => {
    if (show) {
      setTimeRemaining(timeoutInSeconds);
    }
  }, [show, timeoutInSeconds]);

  useEffect(() => {
    if (!show) return;

    const timer = setInterval(() => {
      setTimeRemaining((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [show]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleRefresh = async () => {
    await sendRefreshSession()
  }

  if (!show) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.popup}>
        <h2 className={styles.title}>Session Timeout Warning</h2>
        <p className={styles.message}>
          Your session will expire due to inactivity. Please continue work to stay logged in.
        </p>
        <div className={styles.timer}>{formatTime(timeRemaining)}</div>
        <Button onClick={handleRefresh}>Continue</Button>
      </div>
    </div>
  );
};

export default IdleTimeoutPopup;