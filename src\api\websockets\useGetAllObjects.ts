import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetAllObjects() {
    const [isLoadingAllObjects, setIsLoading] = useState(false);
    const [errorAllObjects, setError] = useState<Error | null>(null);

    const sendGetAllObjects = async () => {
        setIsLoading(true);
        setError(null);

        try {
            sendMessage(
                wsMessages.getAllObjects,
                null
            )
        }
        catch (err) {
            const errorObj = err as Error;
            setError(errorObj);
            throw errorObj;
        } finally {
            setIsLoading(false);
        }
    };

    return { sendGetAllObjects, isLoadingAllObjects, errorAllObjects };
}
