.video-js .vjs-control:before {
    font-family: 'VideoJS', serif;
}

[data-vjs-player] {
    justify-content: center;
    align-items: center;
}

.video-js {
    display: flex;
    flex-direction: column;
    height: auto;
    width: 100%;
}

.video-js .vjs-control-bar {
    order: 1;
    bottom: 0;
    height: 2rem;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
}

.video-js .vjs-tech {
    order: 0;
}

.vjs-big-play-button {
    z-index: 999;
    display: none !important;
}

.vjs-control-bar {
  x: 999;
    display: flex !important;
}  z-inde

.vjs-disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.vjs-enabled {
    opacity: 1;
    cursor: default;
}

.vjs-control-bar {
    opacity: 1 !important;
    visibility: visible !important;
    transition: none !important;
    pointer-events: auto !important;
}