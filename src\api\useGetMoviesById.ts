import { useQuery } from "@tanstack/react-query";
import { UMBRACO_ADRESS } from "../constants/urls";
import useAuth from "./useAuth";

const fetchMovieById = async (id: string, token: string) => {
  if (!id) return { data: null };

  if (!token) {
    throw new Error("No authentication token");
  }

  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch media data");
  }

  return response.json();
};

const useGetMovieById = (id: string) => {
  const { token } = useAuth();

  return useQuery({
    queryKey: ["umbracoMedia", id, token],
    queryFn: () => fetchMovieById(id, token as string),
    enabled: !!id && !!token,
    staleTime: 60000,
  });
};

export default useGetMovieById;
