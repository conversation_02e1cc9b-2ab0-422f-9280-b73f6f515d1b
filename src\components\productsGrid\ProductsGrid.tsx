import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./productsGrid.module.scss";
import useGetShopPageData from "../../api/useGetShopPageData";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import Loader from "../loader/Loader";
import MessageAlert from "../messageAlert/MessageAlert";

const ProductsGrid = () => {
  const { data, isLoading, error } = useGetShopPageData();
  const [activeSort, setActiveSort] = useState<string>("title-asc");
  const [visibleCount, setVisibleCount] = useState<number>(9);
  const navigate = useNavigate();

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  if (isLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  // Pobieramy wszystkie nazwy produktów z danych
  const allProducts: string[] = (data?.flatMap((item: any) =>
    item.properties.addItem?.items?.map(
      (addItem: any) => addItem.content.properties.productName
    )
  ) ?? []).filter((productName: any): productName is string => typeof productName === "string");

  // Usunięcie duplikatów
  const uniqueProducts: string[] = Array.from(new Set(allProducts));

  // Sortowanie produktów
  let sortedProducts = [...uniqueProducts];
  if (activeSort === "title-asc") {
    sortedProducts.sort((a: string, b: string) => a.localeCompare(b));
  } else if (activeSort === "title-desc") {
    sortedProducts.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "newest") {
    // Przykładowe sortowanie – w przypadku posiadania dat można sortować wg. dat
    sortedProducts.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "oldest") {
    sortedProducts.sort((a: string, b: string) => a.localeCompare(b));
  }

  // Paginacja – pobieramy tylko tyle produktów, ile określa visibleCount
  const paginatedProducts = sortedProducts.slice(0, visibleCount);

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 3);
  };

  return (
    <div className={styles.photoGrid}>
      <div className={styles.sortOptions}>
        <p>Sort by:</p>
        {["title-asc", "title-desc", "newest", "oldest"].map((sortType) => (
          <span
            key={sortType}
            className={`${styles.sortOption} ${activeSort === sortType ? styles.active : ""}`}
            onClick={() => handleSortChange(sortType)}
          >
            {sortType === "title-asc"
              ? "Title (A > Z)"
              : sortType === "title-desc"
                ? "Title (Z > A)"
                : sortType.charAt(0).toUpperCase() + sortType.slice(1)}
          </span>
        ))}
      </div>
      <div className={styles.mainContent}>
        <TableContainer component={Paper} sx={{ backgroundColor: "transparent" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Product Name</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedProducts.map((productName: string, index: number) => (
                <TableRow
                  key={index}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { backgroundColor: "#fff" },
                    transition: "background-color 0.2s ease",
                  }}
                  onClick={() => navigate(`/products/${encodeURIComponent(productName)}`)}
                >
                  <TableCell>{productName}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {visibleCount < sortedProducts.length && (
          <div className={styles.loadMore} onClick={handleLoadMore}>
            <p>MORE</p>
            <KeyboardArrowDownIcon />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductsGrid;
