import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './masonryPageMovieProducts.module.scss';
import Header from '../../components/shared/Header';
import MasonryMovieProductsGrid from '../../components/masonryMovieProductsGrid/MasonryMovieProductsGrid';

const MasonryPageMovieProducts: React.FC = () => {
    const { productName } = useParams<{ productName: string }>();

    return (
        <div className={styles.photosPage}>
            <Header />
            <div className={styles.contentWrapper}>
                <div className={styles.container}>
                    <MasonryMovieProductsGrid searchQuery={productName || ""} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPageMovieProducts;
