@import '../../styles/variables';

.fashionComponent {
    width: 100%;
    padding: 0 24px;

    .buttonsContainer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30px 0;
    }

    .optionsContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0 30px 50px;

        .button {
            color: $darkGray;
            font-size: 18px;
            background: none;
            border: none;
            cursor: pointer;

            &:hover {
                color: #333;
            }
        }

        .switchesContainer {
            display: flex;
            align-items: center;

            .group:nth-of-type(2) {
                margin-left: 20px;
            }
        }

        .group {
            display: flex;
            align-items: center;
            gap: 8px;

            .label {
                font-weight: 500;
                color: $darkGray;
            }

            .switch {
                position: relative;
                display: inline-flex;
                align-items: center;
                width: 44px;
                height: 24px;
                padding: 2px;

                .switchInput {
                    opacity: 0;
                    width: 0;
                    height: 0;
                    position: absolute;
                
                    &:checked+.switchSlider {
                        background-color: #3b82f6;
                
                        &:before {
                            transform: translateX(20px);
                        }
                    }
                }

                .switchSlider {
                    position: absolute;
                    cursor: pointer;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: #ccc;
                    transition: 0.2s;
                    border-radius: 24px;
                
                    &:before {
                        position: absolute;
                        content: "";
                        height: 20px;
                        width: 20px;
                        left: 2px;
                        bottom: 2px;
                        background-color: white;
                        transition: 0.2s;
                        border-radius: 50%;
                    }
                }
            }

            .input {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 12px;
                width: 160px;
            
                &:focus {
                    outline: none;
                    border-color: #999;
                }
            }
            
        }
    }
}