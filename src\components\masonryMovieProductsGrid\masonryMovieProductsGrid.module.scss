@import '../../styles/_variables.scss';

.masonryGrid {
    columns: 4;
    gap: 10px;
    background-color: $lightGray;
    position: relative;
    padding: 3px;
    max-width: 1510px;
    margin-left: auto;
    margin-right: auto;

    .gridItem {
        background-color: $white;
        padding: 15px;
        margin-bottom: 20px;
        display: inline-block;
        width: 100%;
        break-inside: avoid;

        img {
            width: 100%;
            display: block;
        }

        .textBlock {
            text-align: center;
            margin-top: 10px;

            h1 {
                color: $black;
                font-size: 20px;
                font-weight: 600;
                margin-top: 10px;
            }

            p {
                color: $softBlue;
                font-size: 18px;
                margin-bottom: 10px;
            }
        }
    }

    @media (max-width: 576px) {
        columns: 2;
    }
}