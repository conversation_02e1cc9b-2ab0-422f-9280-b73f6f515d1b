import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './masonryPageModels.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';
import MasonryModelsGrid from '../../components/masonryModelsGrid/MasonryModelsGrid';

const MasonryPageModels: React.FC = () => {
    const { modelName } = useParams<{ modelName: string }>();

    return (
        <div className={styles.photosPage}>
            <HeaderTraffique />
            <div className={styles.contentWrapper}>
                <div className={styles.container}>
                    <MasonryModelsGrid searchQuery={modelName || ""} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPageModels;
