import { useEffect, useState } from "react";
import PhotoGrid from "../../components/photoGrid/PhotoGrid";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from "./photoGridPage.module.scss";
import useGetShopPageDataWithFilters from "../../api/useGetShopPageDataWithFilters";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import useGetFavorites from "../../api/useGetFavorites";

const PhotoGridPage = () => {
  const {
    data: photosData,
    isLoading,
    error,
  } = useGetShopPageDataWithFilters();
  const { data: favoritesData, isLoading: isFavoritesLoading } =
    useGetFavorites();
  let enrichedPhotos: any[] = [];

  if (photosData && favoritesData) {
    const favoriteIds = new Set(favoritesData.map((fav: any) => fav.key));

    enrichedPhotos = photosData.map((photo: any) => ({
      ...photo,
      isFavorite: favoriteIds.has(photo.id),
    }));
  }

  if (isLoading || isFavoritesLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <PhotoGrid items={enrichedPhotos} />
      </div>
    </div>
  );
};

export default PhotoGridPage;
