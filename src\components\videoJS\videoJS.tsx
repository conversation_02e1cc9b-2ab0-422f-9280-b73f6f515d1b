import React, {forwardRef, useEffect, useImperativeHandle} from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import "./videoJS.css";
import {SessionState} from "../../enums/sessionState";

interface VideoJSProps {
  url: string;
  playing: boolean;
  controls?: boolean;
  autoPlay?: boolean;
  playsinline?: boolean;
  width?: string;
  height?: string;
  fluid?: boolean;
  onReady?: (player: any) => void;
  onProgress?: (state: { playedSeconds: number }) => void;
  hasTrackingResult: (frame: number) => boolean;
  setWaitingForFrame: (frame: number) => void;
  currentFrameRef: React.MutableRefObject<number>;
  videoRef: React.RefObject<any>;
  playerRef: any;
  sessionState: SessionState | null;
}

const VideoJS = forwardRef((props: VideoJSProps, ref) => {
  const {
    url,
    playing,
    controls = true,
    autoPlay = false,
    playsinline = false,
    height = "100%",
    width = "100%",
    fluid = true,
    onReady,
    onProgress,
    hasTrackingResult,
    setWaitingForFrame,
    currentFrameRef,
    videoRef,
    playerRef,
    sessionState
  } = props;

  const [isPlayerReady, setIsPlayerReady] = React.useState(false);
  const animationFrameIdRef = React.useRef<number | null>(null);

  useImperativeHandle(ref, () => ({
    player: playerRef.current,
  }));

  useEffect(() => {
    if (!playerRef.current) {
      const videoElement = document.createElement("video-js");

      if (videoRef.current) {
        videoRef.current.appendChild(videoElement);
      }

      const player = (playerRef.current = videojs(
        videoElement,
        {
          controls,
          autoplay: autoPlay,
          muted: false,
          sources: [{ src: url, type: "video/mp4" }],
          playsinline,
          fluid,
          userActions: {
            doubleClick: false,
            click: false,
          },
          controlBar: {
            pictureInPictureToggle: false,
            fullscreenToggle: false,
            playToggle: true,
            alwaysVisible: true,
          },
        },
        () => {
          setIsPlayerReady(true);
          if (onReady) {
            onReady(player);
          }
        }
      ));

      // Standard timeupdate event (low frequency)
      player.on("timeupdate", () => {
        const playedSeconds = player.currentTime();
        if (typeof playedSeconds === "number") {
          onProgress && onProgress({ playedSeconds });
        }
      });

      // Custom high-frequency timeupdate using requestAnimationFrame for smoother overlay rendering
      let lastTime = 0;

      const updateTime = () => {
        const playedSeconds = player.currentTime();
        if (typeof playedSeconds === "number" && playedSeconds !== lastTime) {
          lastTime = playedSeconds;
          onProgress && onProgress({ playedSeconds });
        }
        animationFrameIdRef.current = requestAnimationFrame(updateTime);
      };

      player.on("play", () => {
        animationFrameIdRef.current = requestAnimationFrame(updateTime);
      });

      player.on("pause", () => {
        if (animationFrameIdRef.current !== null) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null;
        }
      });

      player.on("ended", () => {
        if (animationFrameIdRef.current !== null) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null;
        }
      });
    }

    return () => {
      if (playerRef.current) {
        // Cancel any active animation frame
        if (animationFrameIdRef.current !== null) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null;
        }

        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [url, controls, autoPlay, playsinline, width, height, onReady, onProgress, playerRef, videoRef, fluid, sessionState]);

  useEffect(() => {
    if (playerRef.current) {
      const player = playerRef.current;

      if (url !== player.currentSrc()) {
        player.src([{ src: url, type: "video/mp4" }]);
        player.load();
      }

      if (playing) {
        player
          .play()
          .catch((error: any) => console.warn("Video playback error:", error));
      } else {
        player.pause();
      }
    }
  }, [url, playing, playerRef]);

  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => {

      if (!hasTrackingResult(currentFrameRef.current)) {
        player.pause();
        player.addClass('vjs-waiting')
        player.controls(false);
        setWaitingForFrame(currentFrameRef.current);
      }
    }

    player.on('play', handlePlay);

    return () => {
      player.off('play', handlePlay);
    }

  }, [currentFrameRef, hasTrackingResult, playerRef, setWaitingForFrame]);

  const disableControl = (control: any) => {
    if (!control) return;
    control.el().style.pointerEvents = 'none';
    control.removeClass('vjs-enabled')
    control.addClass('vjs-disabled');
  };

  const enableControl = (control: any) => {
    if (!control) return;
    control.el().style.pointerEvents = 'auto';
    control.removeClass('vjs-disabled');
    control.addClass('vjs-enabled');
  };

  useEffect(() => {
    if (!isPlayerReady || !playerRef.current) return;

    const player = playerRef.current;

    const handleSessionStateChange = () => {
      if (sessionState !== SessionState.IDLE) {
        disableControl(player.controlBar.playToggle);
        disableControl(player.controlBar.progressControl);
        disableControl(player.controlBar.volumePanel);
      } else {
        enableControl(player.controlBar.playToggle);
        enableControl(player.controlBar.progressControl);
        enableControl(player.controlBar.volumePanel);
      }
    };

    const timeout = setTimeout(handleSessionStateChange, 0);
    return () => clearTimeout(timeout);
  }, [sessionState, playerRef, isPlayerReady]);

  console.log("sessionState (from video-js):", sessionState);

  return (
    <div data-vjs-player>
      <div ref={videoRef} style={{
        overflow: "hidden",
      }} />
    </div>
  );
});

export default VideoJS;
