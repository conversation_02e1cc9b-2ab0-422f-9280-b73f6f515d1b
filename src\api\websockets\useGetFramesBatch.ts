import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetFramesBatch() {
  const [isLoadingFramesBatch, setIsLoading] = useState(false);
  const [errorFramesBatch, setError] = useState<Error | null>(null);

  const sendGetFramesBatch = async (frameIdx: number, batchSize: number) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.getFramesBatch,
          {
            "frame_idx": frameIdx,
            "batch_size": batchSize
          }
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj;
    } finally {
      setIsLoading(false);
    }
  };

  return { sendGetFramesBatch, isLoadingFramesBatch, errorFramesBatch };
}
