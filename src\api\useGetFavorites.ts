import { useQuery } from "@tanstack/react-query";
import { UMBRACO_ADRESS } from "../constants/urls";
import useAuth from "./useAuth";

const useGetFavorites = () => {
  const { token } = useAuth();

  const fetchFavorites = async () => {
    try {
      const response = await fetch(`${UMBRACO_ADRESS}umbraco/api/Favorite`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        console.error("Błąd API:", response.status, response.statusText);
        throw new Error(
          `Błąd API: ${response.status} - ${response.statusText}`
        );
      }

      const data = await response.json();

      return data ?? [];
    } catch (error) {
      console.error("Błąd pobierania danych:", error);
      return [];
    }
  };

  return useQuery({
    queryKey: ["getFavorites"],
    queryFn: fetchFavorites,
    enabled: !!token,
  });
};

export default useGetFavorites;
