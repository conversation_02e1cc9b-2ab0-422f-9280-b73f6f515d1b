@import '../../styles/variables';

.photoGrid {
  width: 100%;
  padding: 20px;
  background-color: #fff;
  margin-top: 85px;

  .filters {
    display: flex;
    justify-content: start;
    margin-bottom: 20px;

    a {
      text-align: center;
      padding: 20px;
      width: calc(25% - 30px);
      margin-right: 10px;
      border-radius: 5px;
      color: white;
      font-weight: bold;
      cursor: pointer;
      transition: 0.3s ease;

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        filter: brightness(1.2);
      }
    }

    .published {
      background-color: #8cc63f;
    }

    .unpublished {
      background-color: #ff1d25;
    }

    .videos {
      background-color: #3fa9f5;
    }

    .newPhoto {
      background-color: $darkGray;
    }
  }

  .sortOptions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #777;

    p {
      margin: 10px 0;
      color: #bbb;
    }

    .sortOption {
      cursor: pointer;
      transition: color 0.3s ease;
      color: #000;

      &.active {
        color: #007bff;
      }
    }
  }


  .mainContent {
    background-color: #f5f5f5;
    padding: 15px 30px;

    .link {
      text-decoration: "none";
      color: "inherit"
    }

    .photoGrid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 20px;

      a {
        text-decoration: none;
      }

      .photoCard {
        background-color: white;
        overflow: hidden;
        cursor: pointer;
        transition: transform 0.2s ease-in-out;
        margin: 0 auto;

        &:hover {
          transform: translateY(-5px);
        }

        .photoImageContainer {
          position: relative;
          width: fit-content;

          .label {
            position: absolute;
            top: 10px;
            left: 20px;
            background-color: red;
            z-index: 999;

            p {
              color: #fff;
              padding: 5px 15px;
            }
          }

          .photoImage {
            position: relative;
            height: 550px;
            object-fit: contain;
            display: block;
            margin: 0 auto;
          }
        }

        .status {
          height: 5px;

          &.published {
            background-color: #8cc63f;
          }

          &.unpublished {
            background-color: #ff1d25;
          }

          &.video {
            background-color: #3fa9f5;
          }
        }

        .photoDetails {
          padding: 15px;
          text-align: center;

          h3 {
            font-weight: 500;
            margin-bottom: 10px;
            color: black;
          }

          p {
            font-weight: 500;
            margin: 5px 0;
            color: #CACACE;
          }
        }
      }
    }

    .loadMore {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #000;
      font-weight: 600;
      font-size: 24px;
      padding: 50px 0;
      cursor: pointer;

      svg {
        font-size: 48px;
      }
    }
  }

}