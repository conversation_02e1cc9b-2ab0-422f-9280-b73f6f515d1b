import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Search from '../../components/search/Search';
import MasonryGrid from '../../components/masonryGrid/MasonryGrid';
import styles from './masonryMoviePage.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';
import Header from '../../components/shared/Header';
import MasonryMovieGrid from '../../components/masonryMovieGrid/MasonryMovieGrid';

const MasonryMoviePage = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const initialQuery = searchParams.get('query') || '';
    const initialCity = searchParams.get('city') || 'Everywhere';

    const [searchQuery, setSearchQuery] = useState(initialQuery);
    const [selectedCity, setSelectedCity] = useState(initialCity);

    useEffect(() => {
        setSearchQuery(initialQuery);
    }, [initialQuery]);

    useEffect(() => {
        setSelectedCity(initialCity);
    }, [initialCity]);

    const handleSearch = (query: string) => {
        setSearchParams({ query, city: selectedCity });
        setSearchQuery(query);
    };

    const handleCityChange = (city: string) => {
        setSearchParams({ query: searchQuery, city });
        setSelectedCity(city);
    };

    return (
        <div className={styles.photosPage}>
            <Header />
            <div className={styles.contentWrapper}>
                <Search
                    searchQuery={searchQuery}
                    selectedCity={selectedCity}
                    onSearch={handleSearch}
                    onCityChange={handleCityChange}
                />
                <div className={styles.container}>
                    <MasonryMovieGrid searchQuery={searchQuery} selectedCity={selectedCity} />
                </div>
            </div>
        </div>
    );
};

export default MasonryMoviePage;
