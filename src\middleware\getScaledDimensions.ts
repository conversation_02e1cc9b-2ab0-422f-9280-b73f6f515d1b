export const getScaledDimensions = (videoDimensions: { width: number; height: number }): { width: number; height: number } => {
    const maxHeight = window.innerHeight * 0.8;
    const maxWidth = window.innerWidth - 500;
    const scaleHeight = videoDimensions.height > maxHeight ? maxHeight / videoDimensions.height : 1;
    const scaleWidth = videoDimensions.width > maxWidth ? maxWidth / videoDimensions.width : 1;
    const scale = Math.min(scaleHeight, scaleWidth);
  
    return {
      width: videoDimensions.width * scale,
      height: videoDimensions.height * scale,
    };
  };
  