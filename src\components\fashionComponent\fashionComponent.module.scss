@import "../../styles/variables";

.fashionComponent {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin: 56px 0;
    gap: 5px;

    div {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:nth-last-child(1) {
        width: 100%;
        display: flex;
        justify-content: end;
      }

      .button {
        font-size: 20px;
        letter-spacing: 1px;
        font-weight: 600;
        text-align: center;
        padding: 12px 40px;
        margin-right: 10px;
        color: $white;
        cursor: pointer;
        transition: 0.3s ease;
        background-color: $darkGray;

        &:last-of-type {
          margin-right: 0;
        }

        &:hover {
          filter: brightness(1.2);
        }

        p {
          margin-left: 10px;
        }
      }

      .save {
        background-color: $green;
        width: auto;
      }
    }
  }

  .container {
    background-color: $gray;
    padding: 15px 30px;

    .containerWithImage {
      display: flex;
      margin-bottom: 20px;

      .leftSide {
        canvas {
          display: block;
          max-height: 80vh;
          height: auto;
          margin: 0 auto;
        }
        img {
          display: block;
          max-height: 80vh;
          max-width: 60vw;
          min-width: 40vw;
          width: auto;
          height: auto;
          object-fit: contain;
          display: block;
          margin: 0 auto;
        }
        .imageError {
          border: 1px solid $errorColor;
          border-radius: 6px;
        }
        .uploadContainer {
          background-color: white;
        }
        div {
          display: flex;
          flex-direction: column;
          justify-content: center;

          max-width: 60vw;
          min-width: 40vw;
          height: 100%;

          text-align: center;

          .uploadIcon {
            max-height: 10vh;
            margin: 5px;
            object-fit: contain;
          }

          input {
            display: none;
          }

          .fileUpload {
            font-weight: bold;
            color: #3fa9f5;
            text-decoration: underline;
          }

          .fileUpload:hover {
            color: blue;
          }
        }
      }

      .rightSide {
        padding-left: 20px;
        align-self: start;
        width: 100%;

        p {
          margin-bottom: 10px;
          color: $darkTextColor;
        }

        textarea {
          width: 100%;
          padding: 16px;
          border-radius: 4px;
          background-color: transparent;
          color: #4d4d4d;
          font-family: "Poppins", sans-serif;
          font-size: 16px;
          border: 1px solid #808080;
          resize: none;
          margin-bottom: 16px;
        }
        .textareaError {
          border-color: $errorColor;
        }
      }
    }

    .itemsConteiner {
      display: flex;
      flex-direction: column;
      gap: 20px;
      width: 100%;
      border-radius: 6px;

      .itemContainerHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: bold;
          text-transform: uppercase;
          color: #333;
          margin-top: 10px;
        }
        .clearButton {
          cursor: pointer;
          font-size: 16px;
          font-weight: bold;
          color: #af0000;
          margin-top: 10px;
          &:hover {
            color: #840000;
          }
        }
      }

      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid $darkGray;
        border-radius: 6px;

        .leftSide {
          font-size: 14px;
          color: $darkTextColor;
          padding: 16.5px 14px;

          p {
            font-size: 16px;
            font-weight: bold;
          }
          .error {
            color: $errorColor;
            font-weight: normal;
            font-size: 12px;
          }
        }

        .rightSide {
          display: flex;
          gap: 5px;
          padding-right: 10px;
        }
      }
      .itemError {
        border: 1px solid $errorColor;
      }

      .addItem {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        padding: 10px;
        border: 2px dashed $black;
        border-radius: 6px;
        color: $brightBlue;
        cursor: pointer;
        transition: 0.2s ease;
        &:hover {
          background: $paleGray;
        }
      }
    }
  }
}

.clickedButton {
  background-color: grey !important;
  aspect-ratio: 1 / 1 !important;
  border-radius: 100% !important;
  min-width: 0 !important;
  color: black !important;
}
.toggleButton {
  min-width: 0 !important;
}
.toggleButton.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
