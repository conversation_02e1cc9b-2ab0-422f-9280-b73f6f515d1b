import { useEffect, useState } from "react";
import useGoogleAuth from "./useGoogleAuth";

const PROPERTY_ID = "455433846";

const useGoogleAnalyticsData = () => {
  const { token } = useGoogleAuth();
  const [shopPageViews, setShopPageViews] = useState<Record<string, number>>({});

  useEffect(() => {
    if (!token) return;

    const fetchAnalyticsData = async () => {
      const url = `https://analyticsdata.googleapis.com/v1beta/properties/${PROPERTY_ID}:runReport`;

      const requestBody = {
        dateRanges: [{ startDate: "30daysAgo", endDate: "today" }],
        metrics: [{ name: "eventCount" }],
        dimensions: [{ name: "pagePath" }],
      };

      try {
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();
        console.log(data)

        if (!data.rows) {
          console.warn("⚠️ Brak danych z GA4");
          return;
        }


        const filteredPages = data.rows
          .map((row: any) => ({
            path: row.dimensionValues[0]?.value || "",
            views: parseInt(row.metricValues[0]?.value || "0", 10),
          }))
          .filter((entry: { path: string; }) => entry.path.startsWith("/shop-pages/"))
          .reduce((acc: { [x: string]: any; }, entry: { path: string; views: any; }) => {
            let cleanPath = entry.path.replace("/shop-pages/", "");
            cleanPath = cleanPath.replace(/\/$/, "");
            acc[cleanPath] = entry.views;
            return acc;
          }, {} as Record<string, number>);

        console.log("📊 Przefiltrowane Shop Pages:", filteredPages);
        setShopPageViews(filteredPages);
      } catch (error) {
        console.error("❌ Błąd pobierania danych z GA4:", error);
      }
    };

    fetchAnalyticsData();
  }, [token]);

  return shopPageViews;
};

export default useGoogleAnalyticsData;