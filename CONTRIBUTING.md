# Contributing to Traffique Content Studio Frontend

Thank you for your interest in contributing to the Traffique Content Studio Frontend project! This document provides guidelines and instructions for contributing.

## Code of Conduct

Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md) to foster an inclusive and respectful community.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally
3. Set up the development environment as described in the [README.md](README.md)
4. Create a new branch for your feature or bugfix

## Development Workflow

### Branching Strategy

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - New features
- `bugfix/*` - Bug fixes
- `hotfix/*` - Urgent fixes for production

### Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, missing semicolons, etc)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or correcting tests
- `chore`: Changes to the build process or auxiliary tools

Example:
```
feat(video-editor): add frame-by-frame navigation controls

Implements keyboard shortcuts and UI controls for navigating video frames.

Closes #123
```

### Pull Request Process

1. Update the README.md with details of changes if applicable
2. Update the documentation with details of changes if applicable
3. The PR should work in all supported browsers and devices
4. Ensure all tests pass
5. Get at least one code review from a maintainer

## Code Style

### TypeScript

- Follow the [TypeScript Style Guide](https://github.com/basarat/typescript-book/blob/master/docs/styleguide/styleguide.md)
- Use TypeScript's strict mode
- Define interfaces for all props and state
- Use functional components with hooks instead of class components

### React

- Follow the [React Style Guide](https://reactjs.org/docs/code-style.html)
- Use React hooks for state and side effects
- Keep components small and focused on a single responsibility
- Use React.memo for performance optimization when appropriate

### CSS/SCSS

- Use CSS modules or styled-components for component styling
- Follow the BEM naming convention for CSS classes
- Use variables for colors, spacing, and other design tokens

## Testing

- Write unit tests for all new features and bug fixes
- Aim for high test coverage
- Use React Testing Library for component tests
- Run tests before submitting a PR

## Documentation

- Document all public APIs, components, and functions
- Keep documentation up-to-date with code changes
- Use JSDoc comments for code documentation

## Reporting Bugs

When reporting bugs, please include:

1. A clear and descriptive title
2. Steps to reproduce the issue
3. Expected behavior
4. Actual behavior
5. Screenshots if applicable
6. Environment information (browser, OS, etc.)

## Feature Requests

Feature requests are welcome. Please provide:

1. A clear and descriptive title
2. Detailed description of the proposed feature
3. Any relevant examples or mockups
4. Explanation of why this feature would be useful

## Questions

If you have questions about the codebase or need help, please:

1. Check the documentation first
2. Search for similar issues in the issue tracker
3. Ask in the appropriate communication channels

## License

By contributing to this project, you agree that your contributions will be licensed under the project's license.