@import '../../styles/variables';

.tabsChartsContainer {
    width: 100%;
    margin: 165px 20px 20px 20px;

    .filters {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 20px;
        grid-area: 1 / 2 / 2 / 6;
        margin: 10px 20px;
    
        a {
            font-size: 20px;
            letter-spacing: 1px;
            font-weight: 600;
            text-align: center;
            padding: 12px 40px;
            width: calc(25% - 30px);
            margin-right: 10px;
            color: $white;
            cursor: pointer;
            transition: 0.3s ease;
            width: auto;
    
            &:last-of-type {
                margin-right: 0;
            }
    
            &:hover {
                filter: brightness(1.2);
            }
        }
    
        .videos {
            background-color: $darkGray;
        }
    }
}