import {useCallback, useRef, useState} from "react";
import {ObjectResult, PropagateVideoResponse} from "../types/polygons.type";

export const useTrackingResults = () => {
    const [trackingResults, setTrackingResults] = useState<Record<number, ObjectResult[]>>({});
    const trackingResultsRef = useRef<Record<number, ObjectResult[]>>({});

    const setTrackingResult = useCallback((frameIndex: number, items: ObjectResult[]) => {

        const safeItems = Array.isArray(items) ? items : [];
        trackingResultsRef.current[frameIndex] = safeItems;

        setTrackingResults(prev => ({
            ...prev,
            [frameIndex]: safeItems
        }));
        console.log("CHANGED TRACKING RESULTS")
    }, []);

    const setBatchTrackingResults = useCallback((batch: PropagateVideoResponse[]) => {
        if (!Array.isArray(batch) || batch.length === 0) {
            console.warn("Received empty or invalid batch:", batch);
            return;
        }

        const updates: Record<number, ObjectResult[]> = {};

        for (const result of batch) {
            if (result) {
                updates[result.frameIndex] = Array.isArray(result.results) ? result.results : [];
            }
        }

        Object.assign(trackingResultsRef.current, updates);

        setTrackingResults(prev => ({
            ...prev,
            ...updates
        }));
    }, []);

    const deleteObjectFromTrackingResults = useCallback((objectId: number) => {
        const updatedResults: Record<number, ObjectResult[]> = {};

        console.log("DELETE OBJECT FROM TRACKING RESULTS", objectId);
        console.log("TRACKING RESULTS", trackingResultsRef.current);

        Object.keys(trackingResultsRef.current).forEach((frameIndexStr) => {
            const frameIndex = Number(frameIndexStr);

            updatedResults[frameIndex] = trackingResultsRef.current[frameIndex].filter(
                (result) => result.objectId !== objectId
            );
        });

        console.log("UPDATED TRACKING RESULTS", updatedResults);

        trackingResultsRef.current = updatedResults;
        setTrackingResults(updatedResults);
    }, []);

    return {
        trackingResultsRef,
        trackingResults,
        setTrackingResult,
        setBatchTrackingResults,
        setTrackingResults,
        deleteObjectFromTrackingResults,
    };
};
