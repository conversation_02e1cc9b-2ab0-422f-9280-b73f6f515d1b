import { useRef, useState } from "react";
import styles from './fileUploadPhoto.module.scss';
import DescriptionIcon from "@mui/icons-material/Description";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import useUploadFile from "../../api/useUploadFile";
import SlideshowIcon from '@mui/icons-material/Slideshow';
import { CircularProgress } from "@mui/material";
import useUploadPhotoToPage from "../../api/useUploadPhotoToPage";

type UploadStatus = "select" | "uploading" | "done";

const FileUploadPhoto = ({id}: {id: string}) => {
  const { mutate: uploadFile } = useUploadPhotoToPage();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>("select");
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>): void => {
    event.preventDefault();
    setIsDragOver(false);
    if (event.dataTransfer.files.length > 0) {
      setSelectedFile(event.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>): void => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (): void => {
    setIsDragOver(false);
  };

  const onChooseFile = (): void => {
    inputRef.current?.click();
  };

  const clearFileInput = (): void => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    setSelectedFile(null);
    setProgress(0);
    setUploadStatus("select");
  };

  const handleUpload = (): void => {
    if (!selectedFile) return;

    setUploadStatus("uploading");

    uploadFile({file: selectedFile, pageId: id}, {
      onSuccess: () => {
        setUploadStatus("done");
      },
      onError: () => {
        setUploadStatus("select");
      },
      onSettled: () => {
        setProgress(100);
      },
    });
  };

  return (
    <div className={styles.fileUploadContainer}>
      <div
        className={styles.dropArea}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input ref={inputRef} type="file" onChange={handleFileChange}/>

        {!selectedFile && (
          <button className={styles.fileBtn} onClick={onChooseFile}>
            <SlideshowIcon /> 
            <p>Drag a video here or upload a file</p>
          </button>
        )}

        {selectedFile && (
          <div className={styles.fileCardContainer}>
            <div className={styles.fileCard}>
              <DescriptionIcon className={styles.icon} />

              <div className={styles.fileInfo}>
                <div style={{ flex: 1 }}>
                  <h6>{selectedFile?.name}</h6>

                  <div className={styles.progressBg}>
                    <div className={styles.progress} style={{ width: `${progress}%` }} />
                  </div>
                </div>

                {uploadStatus === "select" ? (
                  <button onClick={clearFileInput}>
                    <CloseIcon className={styles.closeIcon} />
                  </button>
                ) : (
                  <div className={styles.checkCircle}>
                    {uploadStatus === "uploading" ? (
                      `${progress}%`
                    ) : uploadStatus === "done" ? (
                      <CheckCircleIcon style={{ fontSize: "20px" }} />
                    ) : null}
                  </div>
                )}
              </div>
            </div>
            {
              uploadStatus === 'uploading' 
              ? <div className={styles.uploadLoader}><CircularProgress size={24} /></div>
              : <button className={styles.uploadBtn} onClick={handleUpload} disabled={uploadStatus === "done"} style={ uploadStatus === "done" ? {backgroundColor: '#8DC63F'} : undefined }>{uploadStatus === "select" ? "Upload" : "Done"}</button>
            }
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUploadPhoto;
