import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useRefreshSession() {
  const [isLoadingRefresh, setIsLoading] = useState(false);
  const [errorRefresh, setError] = useState<Error | null>(null);

  const sendRefreshSession = async () => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.refreshSession,{}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj;
    } finally {
      setIsLoading(false);
    }
  };

  return { sendRefreshSession, isLoadingRefresh, errorRefresh };
}
