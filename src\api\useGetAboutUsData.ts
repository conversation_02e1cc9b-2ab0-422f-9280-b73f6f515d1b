import { useQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS_PHOTOGRAPHER } from '../constants/urls';

const fetchAboutUs = async () => {
  const response = await fetch(
    `${UMBRACO_ADRESS_PHOTOGRAPHER}umbraco/delivery/api/v2/content?filter=contentType:about&take=1000000`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch shop page data');
  }

  const data = await response.json();

  return data.items;
};

const useGetAboutUsData = () => {
  return useQuery({
    queryKey: ['about'],
    queryFn: fetchAboutUs,
  });
};

export default useGetAboutUsData;
