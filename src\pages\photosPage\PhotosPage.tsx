import { useParams } from "react-router-dom";
import FashionComponent from "../../components/fashionComponent/FashionComponent";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from "./photosPage.module.scss";
import { useGetUmbracoContentQuery } from "../../api/useGetUmbracoContentQuery";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import useGetMovieById from "../../api/useGetMoviesById";
import { useState, useEffect } from "react";
import useGetPhotoById from "../../api/useGetPhotoById";

const PhotosPage = () => {
  const { id } = useParams();
  const [mediaKey, setMediaKey] = useState<string | null>(null);
  const [photoUrl, setPhotoUrl] = useState("");

  const { data: document, isLoading, error } = useGetMovieById(id!);

  const { data: photo, error: photoError } = useGetPhotoById(mediaKey!);

  useEffect(() => {
    if (document != null) {
      const extractedKey = document.values.find(
        (v: any) => v.alias === "shopPagePhoto"
      )?.value?.[0]?.mediaKey;

      if (extractedKey) {
        setMediaKey(extractedKey);
      }
    }
  }, [document]);

  useEffect(() => {
    if (photo) {
      var url = photo.urls?.[0]?.url;
      if (url) {
        setPhotoUrl(url);
      }
    }
  }, [photo]);

  if (isLoading) return <Loader />;
  if (error || photoError)
    return <MessageAlert type="error" message="Error loading photo data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <FashionComponent item={document} photoUrl={photoUrl} />
      </div>
    </div>
  );
};

export default PhotosPage;
