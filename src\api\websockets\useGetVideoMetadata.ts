import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetVideoMetadata() {
    const [isLoadingMetadata, setIsLoading] = useState(false);
    const [errorMetadata, setError] = useState<Error | null>(null);

    const sendGetVideoMetadata = async () => {
        setIsLoading(true);
        setError(null);

        console.log("sendGetVideoMetadata called");

        try {
            sendMessage(
                wsMessages.getVideoMetadata,{}
            )
        }
        catch (err) {
            const errorObj = err as Error;
            setError(errorObj);
            throw errorObj;
        } finally {
            setIsLoading(false);
        }
    };

    return { sendGetVideoMetadata, isLoadingMetadata, errorMetadata };
}
