import { useState } from "react";
import { Modal, Box, Typography, TextField, Button, MenuItem, Select } from "@mui/material";
import useGetShopPageData from "../../api/useGetShopPageData";
import MessageAlert from "../messageAlert/MessageAlert";

interface UmbracoSyncModalProps {
  open: boolean;
  onClose: () => void;
  onSync: (name: string) => void;
}

const UmbracoSyncModal: React.FC<UmbracoSyncModalProps> = ({
  open,
  onClose,
  onSync,
}) => {
  const { data, isLoading, error } = useGetShopPageData();

  const [selectedValue, setSelectedValue] = useState("");

  const handleSync = () => {
    onSync(selectedValue);
    setSelectedValue("");
    onClose();
  };

  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "#4D4D4D",
          borderRadius: 2,
          boxShadow: 24,
          p: 4,
          width: 400,
        }}
      >
        <Typography
          id="modal-title"
          variant="h6"
          component="h2"
          sx={{ color: "#fff" }}
        >
          Synchronize with backend
        </Typography>
        <Typography
          id="modal-description"
          sx={{ mt: 1, mb: 2, color: "#808080" }}
        >
          Select a content to synchronize with backend.
        </Typography>
        <Select
          value={selectedValue}
          onChange={(e) => setSelectedValue(e.target.value)}
          displayEmpty
          fullWidth
          sx={{
            bgcolor: "#fff",
            color: "#000",
            borderRadius: 1,
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "#fff",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: "#fff",
            },
          }}
        >
          <MenuItem value="" disabled>
            Select Content
          </MenuItem>
          {data?.map((option: any, index: number) => (
            <MenuItem key={index} value={option.id}>
              {option.name}
            </MenuItem>
          ))}
        </Select>
        <Box display="flex" justifyContent="flex-end" mt={2}>
          <Button
            onClick={onClose}
            sx={{
              mr: 1,
              color: "#fff",
              borderColor: "#fff",
              "&:hover": { borderColor: "#808080", color: "#808080" },
            }}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSync}
            sx={{ bgcolor: "#808080", "&:hover": { bgcolor: "#7F7978" } }}
          >
            Synchronize
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default UmbracoSyncModal;