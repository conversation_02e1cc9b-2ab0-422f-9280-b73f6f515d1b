import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { documentParentId, UMBRACO_ADRESS } from "../constants/urls";

interface UploadResponse {
  message: string;
  documentKey: string;
  mediaKey: string;
  mediaUrl: string;
}

interface UploadArgs {
  file: File;
  name: string;
}

export const createAndUpload = async ({
  file,
  name,
}: UploadArgs): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await axios.post<UploadResponse>(
    `${UMBRACO_ADRESS}umbraco/api/CreateDocumentWithMedia/create`,
    formData,
    {
      params: {
        documentTypeAlias: "addShopPage",
        documentName: name,
        parentKey: documentParentId,
        mediaFieldAlias: "photo",
      },
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  console.log(response.data);
  return response.data;
};

const useCreateAndUpload = () => {
  return useMutation<UploadResponse, Error, UploadArgs>({
    mutationFn: createAndUpload,
  });
};

export default useCreateAndUpload;
