@import "../../styles/_variables.scss";

.fileUploadContainer {
  width: 100%;
  height: 100%;
  padding: 20px;

  .dropArea {
    height: 100%;
    position: relative;

    input {
      display: none;
    }

    .fileCardContainer {
      display: flex;
      align-items: center;

      .fileCard {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 15px;
        color: $black;
        background-color: $white;
        border: 1px solid $softPurple;
        border-radius: 6px;
        padding: 8px 15px;

        .fileInfo {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 15px;

          h6 {
            flex: 1;
            font-size: 13px;
            font-weight: 400;
          }

          .progressBg {
            width: 100%;
            height: 5px;
            background-color: $softBlack;
            border-radius: 8px;
            margin-top: 8px;

            .progress {
              width: 0%;
              height: 5px;
              background-color: $darkGray;
              border-radius: 8px;
              transition: width 0.5s ease;
            }
          }

          .closeIcon {
            font-size: 18px;
            cursor: pointer;
          }

          button,
          .checkCircle {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            color: $darkGray;
            background-color: $gray;
            border: none;
            border-radius: 30px;
          }
        }
      }

      .uploadBtn {
        font-size: 16px;
        font-weight: 500;
        color: $white;
        font-weight: bold;
        background-color: $darkGray;
        border: none;
        padding: 15px;
        cursor: pointer;
        border-radius: 6px;
        margin-left: 20px;
      }

      .uploadLoader {
        margin: 0 10px 0 40px;
      }
    }

    .fileBtn {
      width: 100%;
      height: 100%;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 15px;
      color: $darkGray;
      background-color: $lightGrayBlue;
      border: 1px solid $lightGrayBlue;
      cursor: pointer;
      transition: 0.2s;

      &:hover {
        background-color: $warmGray;
        border: 1px solid $black;
        color: $white;
      }

      svg {
        font-size: 200px;
        color: $black;
      }

      p {
        font-size: 24px;
        letter-spacing: 1px;
        color: $brightBlue;
      }

      span {
        width: 50px;
        height: 50px;
        font-size: 30px;
        color: $darkGray;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 25px;
        background-color: $veryLightPurple;
      }
    }

    .icon {
      font-size: 30px;
      color: $darkGray;
    }

    .errorMessage {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      padding: 15px;
      background-color: rgba(255, 0, 0, 0.1);
      border-radius: 6px;
      border: 1px solid #ff0000;
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 100;
      width: 80%;
      max-width: 500px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

      svg {
        color: #ff0000;
        margin-right: 10px;
        font-size: 24px;
      }

      p {
        color: #ff0000;
        font-size: 16px;
        font-weight: 500;
        flex: 1;
      }

      button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        margin-left: 10px;

        svg {
          color: #ff0000;
          font-size: 18px;
        }

        &:hover svg {
          color: darken(#ff0000, 10%);
        }
      }
    }
  }
}
