import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import WordCloudComponent from '../wordCloud/WordCloud';
import DateGrid from '../dateGrid/DateGrid';

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3, backgroundColor: 'grey.200' }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`,
    };
}

export default function BasicTabs() {
    const [value, setValue] = React.useState(0);

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const words = [
        { text: "RIHANNA", weight: 15 },
        { text: "PHARELL", weight: 12 },
        { text: "SAMUEL ROSS", weight: 10 },
        { text: "STREETWEAR", weight: 9 },
        { text: "HIPHOP", weight: 8 },
        { text: "BASKETBALL", weight: 8 },
        { text: "NIKE", weight: 7 },
        { text: "MBDTF KANYE", weight: 7 },
        { text: "LGBT", weight: 6 },
        { text: "CHLOE", weight: 6 },
        { text: "GOLF WANG", weight: 6 },
        { text: "RAF SIMMONS", weight: 6 },
        { text: "RAF X ADIDAS", weight: 5 },
        { text: "PHOTOGRAPHY", weight: 5 },
        { text: "DE LA SOUL", weight: 5 },
        { text: "EAST LONDON", weight: 4 },
        { text: "BERLIN", weight: 4 },
        { text: "TEL AVIV", weight: 4 },
        { text: "MAUERPARK", weight: 4 },
        { text: "WILDENBRUCHSTR", weight: 3 },
        { text: "WWD", weight: 3 },
        { text: "JEANS", weight: 3 },
        { text: "HAIR COLOUR", weight: 3 }
    ];

    return (
        <div style={{ maxWidth: '1830px', marginLeft: 'auto', marginRight: 'auto' }}>
            <Box sx={{ width: '100%' }}>
                <Box>
                    <Tabs
                        value={value}
                        onChange={handleChange}
                        aria-label="basic tabs example"
                        sx={{
                            '.MuiTabs-indicator': {
                                display: 'none', // Ukrycie wskaźnika
                            },
                            display: 'flex',
                        }}
                    >
                        {['LONDON', 'PARIS', 'NEW YORK', 'BERLIN', 'MORE'].map(
                            (label, index) => (
                                <Tab
                                    key={label}
                                    label={label}
                                    {...a11yProps(index)}
                                    sx={{
                                        flexGrow: 1,
                                        textAlign: 'center',
                                        color: 'grey',
                                        border: '1px solid grey',
                                        borderBottom: value === index ? 'none' : '0.5px solid grey',
                                        marginBottom: value === index ? '0px' : '3px',
                                        fontSize: '20px',
                                        minHeight: '70px',
                                        marginRight: '10px',
                                        '&.Mui-selected': {
                                            color: 'black',
                                            backgroundColor: 'grey.200',
                                            border: 'none',
                                        },
                                    }}
                                />
                            )
                        )}
                    </Tabs>
                </Box>
                <CustomTabPanel value={value} index={0}>
                    <DateGrid />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                    <DateGrid />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                    <DateGrid />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={3}>
                    <DateGrid />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={4}>
                    <DateGrid />
                </CustomTabPanel>
            </Box>
        </div>
    );
}
