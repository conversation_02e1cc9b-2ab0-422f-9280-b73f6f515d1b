import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS, videoTemplateId } from '../constants/urls';

const useUpdateVideoDocument = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updateVideoDocument = async (
        id: string,
        input: any,
        temporaryFileId: string,
        data: {
            date: string;
            pageTitle: string;
            client: string;
            videographer: string;
            video: string;
            addItem?: Array<{
                minatureImage: any;
                affiliateLink: string;
                productName: string;
                displayedName: string;
                brand: string;
            }>;
        }
    ) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const jsonString = JSON.stringify(input);
        const utf8Bytes = new TextEncoder().encode(jsonString);
        const binary = Array.from(utf8Bytes)
          .map((byte) => String.fromCharCode(byte))
          .join("");
        const base64 = btoa(binary);

        const requestBody = {
            template: {
                id: videoTemplateId,
            },
            cultures: null,
            values: [
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "date",
                    culture: null,
                    segment: null,
                    value: data.date,
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "video",
                    culture: null,
                    segment: null,
                    value: data.video,
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "pageTitle",
                    culture: null,
                    segment: null,
                    value: data.pageTitle,
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "client",
                    culture: null,
                    segment: null,
                    value: data.client,
                },
                {
                    editorAlias: "Umbraco.TextBox",
                    alias: "videographer",
                    culture: null,
                    segment: null,
                    value: data.videographer,
                },
                {
                    editorAlias: "Umbraco.BlockList",
                    alias: "addItem",
                    culture: null,
                    segment: null,
                    value: {
                        layout: {
                            "Umbraco.BlockList": data.addItem
                                ? data.addItem.map(() => ({ contentKey: "b4bb0c68-236a-475d-ab90-f2579ac41c49" }))
                                : [],
                        },
                        contentData: data.addItem
                            ? data.addItem.map(item => ({
                                key: "b4bb0c68-236a-475d-ab90-f2579ac41c49",
                                contentTypeKey: "c54ec5a4-edb7-4d85-8621-317429571204",
                                values: [
                                    {
                                        culture: null,
                                        segment: null,
                                        alias: "minatureImage",
                                        editorAlias: "Umbraco.MediaPicker3",
                                        value: item.minatureImage,
                                    },
                                    {
                                        culture: null,
                                        segment: null,
                                        alias: "affiliateLink",
                                        editorAlias: "Umbraco.TextBox",
                                        value: item.affiliateLink,
                                    },
                                    {
                                        culture: null,
                                        segment: null,
                                        alias: "productName",
                                        editorAlias: "Umbraco.TextBox",
                                        value: item.productName,
                                    },
                                    {
                                        culture: null,
                                        segment: null,
                                        alias: "displayedName",
                                        editorAlias: "Umbraco.TextBox",
                                        value: item.displayedName,
                                    },
                                    {
                                        culture: null,
                                        segment: null,
                                        alias: "brand",
                                        editorAlias: "Umbraco.TextBox",
                                        value: item.brand,
                                    },
                                ],
                            }))
                            : [],
                    },
                },
                {
                    editorAlias: "Umbraco.UploadField",
                    alias: "uploadVideoMap",
                    culture: null,
                    segment: null,
                    value: {
                      src: `data:application/json;base64,${base64}`,
                      temporaryFileId
                    }
                  }
            ],
            expose: [
                { contentKey: "b4bb0c68-236a-475d-ab90-f2579ac41c49", culture: null, segment: null },
            ],
            layout: {
                "Umbraco.BlockList": [
                    { contentKey: "b4bb0c68-236a-475d-ab90-f2579ac41c49" },
                ],
            },
            settingsData: [],
            variants: [
                {
                    culture: null,
                    segment: null,
                    state: "Published",
                    name: "VideoTestPage (7)",
                },
            ],
        };

        try {
            let response = await fetch(
                `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
                {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": `Bearer ${currentToken}`,
                    },
                    body: JSON.stringify(requestBody),
                }
            );

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(
                        `${UMBRACO_ADRESS}umbraco/management/api/v1/document/${id}`,
                        {
                            method: "PUT",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": `Bearer ${currentToken}`,
                            },
                            body: JSON.stringify(requestBody),
                        }
                    );
                }
                if (!response.ok) {
                    throw new Error(`Error: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updateVideoDocument, loading, error, success };
};

export default useUpdateVideoDocument;
