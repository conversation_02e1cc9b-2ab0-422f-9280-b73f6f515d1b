import { Divider } from '@mui/material';
import styles from './header.module.scss'
import logo from '../../assets/images/logo-clique.png'
import { Link } from 'react-router-dom';
import CliqueSearchBar from '../cliqueSearchBar/CliqueSearchBar';

const Header = () => {
  return (
    <div className={styles.header}>
      <div className={styles.container}>
        <Link to={'/clique'}>        
          <img
            src={logo}
            alt="Traffique logo"
            className={styles.logo}
          />
        </Link>
        
        <CliqueSearchBar />
      </div>
      <Divider />
    </div>
  );
};

export default Header;
