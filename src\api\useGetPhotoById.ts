import { useQuery } from "@tanstack/react-query";
import { UMBRACO_ADRESS } from "../constants/urls";
import useAuth from "./useAuth";

export const fetchPhotoById = async (id: string, token: string) => {
  if (!id) return { data: null };

  if (!token) {
    throw new Error("No authentication token");
  }

  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/management/api/v1/media/${id}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch media data");
  }

  return response.json();
};

const useGetPhotoById = (id: string) => {
  const { token } = useAuth();

  return useQuery({
    queryKey: ["umbracoMedia", id, token],
    queryFn: () => fetchPhotoById(id, token as string),
    enabled: !!id && !!token,
    staleTime: 60000,
  });
};

export default useGetPhotoById;
