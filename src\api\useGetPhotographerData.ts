import { useQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS } from '../constants/urls';

const fetchPhotographerInfo = async () => {
  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/delivery/api/v2/content?filter=contentType:photographerInfo&take=1000000`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch shop page data');
  }

  const data = await response.json();

  return data.items;
};

const useGetPhotographerData = () => {
  return useQuery({
    queryKey: ['photographerInfo'],
    queryFn: fetchPhotographerInfo,
  });
};

export default useGetPhotographerData;
