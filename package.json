{"name": "sam2-editor", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@ffmpeg/ffmpeg": "^0.12.10", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@tanstack/react-query": "^5.62.8", "@tanstack/react-query-devtools": "^5.62.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.105", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "axios": "^1.7.4", "base64-arraybuffer": "^1.0.2", "dotenv": "^16.5.0", "event-source-polyfill": "^1.0.31", "gapi-script": "^1.2.0", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.1.1", "react-scripts": "5.0.1", "recharts": "^2.15.0", "sass": "^1.83.0", "typescript": "^4.9.5", "video.js": "^8.19.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@shadcn/ui": "^0.0.4", "@types/event-source-polyfill": "^1.0.5", "@types/react-router-dom": "^5.3.3"}}