import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { UMBRACO_ADRESS } from '../constants/urls';

const uploadPhotoToPage = async ({ file, pageId }: { file: File; pageId: string }): Promise<string> => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    await axios.post(
      `${UMBRACO_ADRESS}umbraco/api/FileToPage/upload?pageId=${pageId}&fieldAlias=shopPagePhoto`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return 'Data sent successfully';
  } catch (error) {
    console.error('Error in uploadPhotoToPage:', error);
    throw new Error('Failed to send data');
  }
};

const useUploadPhotoToPage = () => {
  return useMutation<string, Error, { file: File; pageId: string }>({
    mutationFn: uploadPhotoToPage,
  });
};

export default useUploadPhotoToPage;
