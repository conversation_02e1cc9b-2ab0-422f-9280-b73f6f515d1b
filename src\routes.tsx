import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PhotosPage from './pages/photosPage/PhotosPage';
import TabsPage from "./pages/tabsPage/TabsPage";
import ChartPage from './pages/chartPage/ChartPage';
import WordCloudPage from './pages/wordCloudPage/WordCloudPage';
import PhotoGridPage from './pages/photoGridPage/PhotoGridPage';
import MasonryPage from './pages/masonryPage/MasonryPage';
import MainPage from './pages/mainPage/MainPage';
import PhotographerGridPage from './pages/photographerGridPage/PhotographerGridPage';
import PhotographersPage from './pages/photographersPage/PhotographersPage';
import BrandsGridPage from './pages/brandsGridPage/BrandsGridPage';
import ProductsGridPage from './pages/productsGridPage/ProductsGridPage';
import ProductsPage from './pages/productsPage/ProductsPage';
import HowItWorksPage from './pages/howItWorksPage/HowItWorksPage';
import QuickChatPage from './pages/quickChatPage/QuickChatPage';
import AboutUsPage from './pages/aboutUsPage/AboutUsPage';
import PrivacyPolicyPage from './pages/privacyPolicyPage/PrivacyPolicyPage';
import ModelsGridPage from './pages/modelsGridPage/ModelsGridPage';
import HomePage from './pages/homePage/HomePage';
import { CliqueVideosPage } from './pages/cliqueVideosPage/CliqueVideosPage';
import FavesPage from './pages/favesPage/FavesPage';
import DatePage from './pages/datePage copy/datePage';
import MasonryPageModels from './pages/masonryPage copy/MasonryPageModels';
import MasonryPageProducts from './pages/masonryPageProducts/MasonryPageProducts';
import MasonryPageBrands from './pages/masonryPageBrands/MasonryPageBrands';
import FormPage from './pages/formPage/FormPage';
import MovieBrandsGridPage from './pages/movieBrandsGridPage/MovieBrandsGridPage';
import MasonryPageMovieBrands from './pages/masonryPageMovieBrands/MasonryPageMovieBrands';
import MovieProductsGridPage from './pages/movieProductsGridPage/MovieProductsGridPage';
import MasonryPageMovieProducts from './pages/masonryPageMovieProducts/MasonryPageMovieProducts';
import MasonryMoviePage from './pages/masonryMoviePage/MasonryMoviePage';
import { CliqueMoviesPage } from './pages/cliqueMoviesPage/CliqueMoviesPage';

const AppRoutes = () => {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<PhotoGridPage />} />
                <Route path="/clique-video-editor/:id" element={<MainPage />} />
                <Route path="/clique-add-video" element={<CliqueVideosPage />} />
                <Route path="/clique" element={<CliqueMoviesPage />} />
                <Route path="/photos-page/:id" element={<PhotosPage />} />
                <Route path="/photos-page" element={<PhotosPage />} />
                <Route path="/tabs-page" element={<TabsPage />} />
                <Route path="/charts" element={<ChartPage />} />
                <Route path="/clouds" element={<WordCloudPage />} />
                <Route path="/masonry" element={<MasonryPage />} />
                <Route path="/masonryMovie" element={<MasonryMoviePage />} />
                <Route path="/models/:modelName" element={<MasonryPageModels />} />
                <Route path="/products/:productName" element={<MasonryPageProducts />} />
                <Route path="/brands/:brandName" element={<MasonryPageBrands />} />
                <Route path="/movieBrands/:brandName" element={<MasonryPageMovieBrands />} />
                <Route path="/movieProducts/:productName" element={<MasonryPageMovieProducts />} />
                <Route path="/photographers" element={<PhotographerGridPage />} />
                <Route path="/products" element={<ProductsGridPage />} />
                <Route path="/products/:name" element={<ProductsPage />} />
                <Route path="/brands" element={<BrandsGridPage />} />
                <Route path="/movieBrands" element={<MovieBrandsGridPage />} />
                <Route path="/movieProducts" element={<MovieProductsGridPage />} />
                <Route path="/products" element={<ProductsGridPage />} />
                <Route path="/products/:name" element={<ProductsPage />} />
                <Route path="/photographers-page/:id" element={<PhotographersPage />} />
                <Route path="/how-it-works" element={<HowItWorksPage />} />
                <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
                <Route path="/about-us" element={<AboutUsPage />} />
                <Route path="/quick-chat" element={<QuickChatPage />} />
                <Route path="/models" element={<ModelsGridPage />} />
                <Route path="/home" element={<HomePage />} />
                <Route path="/faves" element={<FavesPage />} />
                <Route path="/date" element={<DatePage />} />
                <Route path="/form" element={<FormPage />} />
            </Routes>
        </Router>
    );
};

export default AppRoutes;
