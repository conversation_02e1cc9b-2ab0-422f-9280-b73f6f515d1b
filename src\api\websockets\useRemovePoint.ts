import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";
import {ISelectionPoint} from "../../types/selectionPoint";

export function useRemovePoint() {
  const [isLoadingRemovePoint, setIsLoading] = useState(false);
  const [errorRemovePoint, setError] = useState<Error | null>(null);

  const sendRemovePoint = async (point: ISelectionPoint) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.removePoint,
          {
            "frame_index": point.frameIndex,
            "object_id": point.objectId,
            "label": point.label,
            "coordinates": {
                "x": point.coordinates.x,
                "y": point.coordinates.y,
            },
          }
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendRemovePoint, isLoadingRemovePoint, errorRemovePoint };
}
