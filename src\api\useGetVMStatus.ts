import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { FUNCTION_APP_BASE_URL } from '../constants/urls';

const fetchVMStatus = async () => {
  const response = await axios.get(`${FUNCTION_APP_BASE_URL}/vm/statuscheck?code=eymTv4nx0Bxt54_cdMnuaU0AK9KN-of3F4KynBVGJuwjAzFuQnHSyQ==`);
  return response.data;
};

export const useGetVMStatus = () => {
  return useQuery({
    queryKey: ['vmStatus'],
    queryFn: fetchVMStatus,
  });
};
