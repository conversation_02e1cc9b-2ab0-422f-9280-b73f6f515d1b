import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/document`;

const useUpdateHomeItem = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error2, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updateHomeItem = async (id: string, data: { heading: string; paragraph: string; paragraph2: string; imageTitle: string; dateLocation: string; authorName: string; }) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        if (!token) {
            await fetchToken();
            return;
        }

        const requestBody = {
            "values": [
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "heading",
                    "value": {
                        "markup": `<h1>${data.heading}</h1>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph",
                    "value": {
                        "markup": `<p><strong>${data.paragraph}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph2",
                    "value": {
                        "markup": `<p><strong>${data.paragraph2}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.TextBox",
                    "culture": null,
                    "segment": null,
                    "alias": "imageTitle",
                    "value": `${data.imageTitle}`
                },
                {
                    "editorAlias": "Umbraco.TextBox",
                    "culture": null,
                    "segment": null,
                    "alias": "dateLocation",
                    "value": `${data.dateLocation}`
                },
                {
                    "editorAlias": "Umbraco.TextBox",
                    "culture": null,
                    "segment": null,
                    "alias": "authorName",
                    "value": `${data.authorName}`
                },
            ],
            "variants": [
                {
                    "culture": null,
                    "segment": null,
                    "state": "Published",
                    "name": "Home - Traffique"
                }
            ],
            "template": {
                "id": "aa8ea73b-64c7-44c7-8981-205a2b910a7e"
            }
        }

        try {
            const response = await fetch(`${API_URL}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`,
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                }
                throw new Error(`Error: ${response.statusText}`);
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };


    return { updateHomeItem, loading, error2, success };
};

export default useUpdateHomeItem;
