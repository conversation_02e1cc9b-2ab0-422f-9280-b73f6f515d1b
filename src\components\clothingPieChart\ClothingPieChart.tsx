import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

const categories = [
    "Tops", "Accessories", "Trousers", "Coats", "Shoes",
    "Jackets", "Denim", "Jumpsuits", "Bags", "Shorts"
];

const generateRandomData = () => {
    return categories.map((name) => ({
        name,
        value: Math.floor(Math.random() * 500) + 50,
    }));
};

const COLORS = [
    "#82ca9d",
    "#8884d8",
    "#ff8042",
    "#0088FE",
    "#FFBB28",
    "#FF4444",
    "#66CCFF",
    "#AA88FF",
    "#FFA07A",
    "#77DD77",
];

const ClothingPieChart: React.FC = () => {
    const data = generateRandomData();
    const total = data.reduce((sum, item) => sum + item.value, 0);

    const formattedData = data.map((item) => ({
        ...item,
        percentage: ((item.value / total) * 100).toFixed(2) + "%",
    }));

    return (
        <div style={{ textAlign: "center", display: "flex", justifyContent: "center", }}>
            <PieChart width={600} height={600}>
                <Legend layout="horizontal"
                    verticalAlign="top"
                    align="center" />
                <Tooltip />
                <Pie
                    data={formattedData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={150}
                    innerRadius={100}
                    fill="#8884d8"
                    label={(entry) => `${entry.name} (${entry.percentage})`}
                >
                    {formattedData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                </Pie>
            </PieChart>
        </div>
    );
};

export default ClothingPieChart;
