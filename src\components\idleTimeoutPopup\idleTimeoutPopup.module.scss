@import '../../styles/_variables.scss';

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.message {
  margin-bottom: 1.5rem;
}

.timer {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #ff3b30;
}

.button {
    font-size: 20px;
    letter-spacing: 1px;
    font-weight: 600;
    text-align: center;
    padding: 12px 40px;
    color: $white;
    cursor: pointer;
    transition: 0.3s ease;
    background-color: $darkGray;
    display: flex;

    &:last-of-type {
        margin-right: 0;
    }

    &:hover {
        filter: brightness(1.2);
    }
}