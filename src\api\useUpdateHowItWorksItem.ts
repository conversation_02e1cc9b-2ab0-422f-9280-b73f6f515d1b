import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/document`;

const useUpdateHowItWorksItem = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error2, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updateHowItWorksItem = async (
        id: string,
        data: {
            heading: string;
            paragraph: string;
            paragraph2: string;
            heading2: string;
            paragraph3: string;
            paragraph4: string;
            heading3: string;
            paragraph5: string;
        }
    ) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const requestBody = {
            "values": [
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "heading",
                    "value": {
                        "markup": `<h1>${data.heading}</h1>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph",
                    "value": {
                        "markup": `<p>${data.paragraph}</p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph2",
                    "value": {
                        "markup": `<h2>${data.paragraph2}</h2>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "heading2",
                    "value": {
                        "markup": `<h2>${data.heading2}</h2>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph3",
                    "value": {
                        "markup": `<p>${data.paragraph3}</p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph4",
                    "value": {
                        "markup": `<p>${data.paragraph4}</p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "heading3",
                    "value": {
                        "markup": `<h2>${data.heading3}</h2>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph5",
                    "value": {
                        "markup": `<p>${data.paragraph5}</p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
            ],
            "variants": [
                {
                    "culture": null,
                    "segment": null,
                    "state": "Published",
                    "name": "How It Works - Traffique"
                }
            ],
            "template": {
                "id": "a6f4e13e-8612-47d7-9c10-371b0fd9c9e2"
            }
        };

        try {
            let response = await fetch(`${API_URL}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    });
                }
                if (!response.ok) {
                    throw new Error(`Error: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updateHowItWorksItem, loading, error2, success };
};

export default useUpdateHowItWorksItem;
