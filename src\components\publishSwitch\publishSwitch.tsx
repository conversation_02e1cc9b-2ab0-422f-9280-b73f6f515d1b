import styles from "./publishSwitch.module.scss";

interface PublishSwitchProps {
  isPublished: boolean;
  handleSwitchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const PublishSwitch = ({
  isPublished,
  handleSwitchChange,
}: PublishSwitchProps) => {
  return (
    <div className={styles.switchesContainer}>
      <span className={styles.label}>UNPUBLISH</span>
      <label className={styles.switch}>
        <input
          className={styles.switchInput}
          type="checkbox"
          checked={isPublished}
          onChange={handleSwitchChange}
        />
        <span className={styles.switchSlider}></span>
      </label>
      <span className={styles.label}>PUBLISH</span>
    </div>
  );
};
export default PublishSwitch;
