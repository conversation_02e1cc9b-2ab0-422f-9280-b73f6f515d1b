import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { HOST_ADRESS, UMBRACO_ADRESS } from '../constants/urls';
import {getConnectionUrl} from "../websockets/wsClient";

const syncWithUmbraco = async (sessionId: string, pageId: string) => {
  try {
    const response = await axios.get(`${getConnectionUrl()}/download_result/${sessionId}`, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data], { type: 'application/json' });
    const formData = new FormData();
    formData.append('file', blob, 'result.json');

    await axios.post(`${UMBRACO_ADRESS}umbraco/api/FileToPage/upload?pageId=${pageId}&fieldAlias=uploadVideoMap`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return 'Data sent successfully';
  } catch (error) {
    console.error('Error in fetchAndSendData:', error);
    throw new Error('Failed to fetch or send data');
  }
};

const useSyncWithUmbraco = () => {
    return useMutation({
      mutationFn: ({ sessionId, pageId }: { sessionId: string; pageId: string }) =>
        syncWithUmbraco(sessionId, pageId),
    });
  };

export default useSyncWithUmbraco;
