import styles from "./favesPage.module.scss";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import PhotoGrid from "../../components/photoGrid/PhotoGrid";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import Loader from "../../components/loader/Loader";
import useGetShopPageDataWithFilters from "../../api/useGetShopPageDataWithFilters";
import useGetFavorites from "../../api/useGetFavorites";

const FavesPage = () => {
  const {
    data: photosData,
    isLoading,
    error,
  } = useGetShopPageDataWithFilters();

  const { data: favoritesData, isLoading: isFavoritesLoading } =
    useGetFavorites();

  let enrichedPhotos: any[] = [];

  if (photosData && favoritesData) {
    const favoriteIds = favoritesData.map((fav: { key: string }) => fav.key);

    enrichedPhotos = photosData
      .filter((photo: any) => favoriteIds.includes(photo.id))
      .map((photo: any) => ({
        ...photo,
        isFavorite: true,
      }));
  }

  if (isLoading || isFavoritesLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <PhotoGrid items={enrichedPhotos} />
      </div>
    </div>
  );
};

export default FavesPage;
