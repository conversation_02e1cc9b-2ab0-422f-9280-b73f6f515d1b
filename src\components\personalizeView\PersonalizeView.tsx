import React, { useState } from "react";

const PerosonalizeView: React.FC = () => {
    const [preferences, setPreferences] = useState<{ [key: string]: boolean }>({});
    const [feedback, setFeedback] = useState("");

    const handleCheckboxChange = (key: string) => {
        setPreferences((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        alert("Form submitted! Check the console for details.");
    };

    return (
        <form onSubmit={handleSubmit} style={{ maxWidth: "1000px", margin: "0 auto", fontFamily: "Arial, sans-serif", fontWeight: '600', color: 'black', textTransform: 'uppercase' }}>
            <div style={{ marginBottom: "10px" }}>
                <h3 style={{ marginBottom: "10px", borderBottom: '1px solid black', paddingBottom: '10px' }}>PERSONALISE MY VIEW</h3>
                <div style={{ display: 'flex', justifyContent: 'space-between', backgroundColor: '#B5B8BA', padding: '40px 40px' }}>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in London"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in London")}
                            style={{ marginRight: "8px" }}
                        />
                        The latest captures in ..
                    </label>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in London"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in London")}
                            style={{ marginRight: "8px" }}
                        />
                        London
                    </label>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in Paris"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in Paris")}
                            style={{ marginRight: "8px" }}
                        />
                        Paris
                    </label>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in Berlin"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in Berlin")}
                            style={{ marginRight: "8px" }}
                        />
                        Berlin
                    </label>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in NYC"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in NYC")}
                            style={{ marginRight: "8px" }}
                        />
                        NYC
                    </label>
                    <label style={{ display: "block", marginBottom: "5px" }}>
                        <input
                            type="checkbox"
                            checked={preferences["Latest Captures in All"] || false}
                            onChange={() => handleCheckboxChange("Latest Captures in All")}
                            style={{ marginRight: "8px" }}
                        />
                        All
                    </label>
                </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'start', backgroundColor: '#EBECED', padding: '40px 40px', gap: '115px', marginBottom: '10px' }}>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Most Viewed Trendsetters"] || false}
                        onChange={() => handleCheckboxChange("Most Viewed Trendsetters")}
                        style={{ marginRight: "8px" }}
                    />
                    Most Viewed Trendsetters
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Most Searched Cities"] || false}
                        onChange={() => handleCheckboxChange("Most Searched Cities")}
                        style={{ marginRight: "8px" }}
                    />
                    Most Searched Cities
                </label>
            </div>

            <div style={{ display: 'flex', justifyContent: 'start', backgroundColor: '#EBECED', padding: '40px 40px', gap: '115px', marginBottom: '10px' }}>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Most Worn Brands"] || false}
                        onChange={() => handleCheckboxChange("Most Worn Brands")}
                        style={{ marginRight: "8px" }}
                    />
                    Most Worn Brands
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Street Styles"] || false}
                        onChange={() => handleCheckboxChange("Top Street Styles")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Street Styles
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Inspo"] || false}
                        onChange={() => handleCheckboxChange("Top Inspo")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Inspo
                </label>
            </div>

            <div style={{ display: 'flex', justifyContent: 'start', backgroundColor: '#EBECED', padding: '40px 40px', gap: '115px', marginBottom: '10px' }}>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Categories"] || false}
                        onChange={() => handleCheckboxChange("Top Categories")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Categories
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Most Searched Items"] || false}
                        onChange={() => handleCheckboxChange("Most Searched Items")}
                        style={{ marginRight: "8px" }}
                    />
                    MOST Searched Items
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Music"] || false}
                        onChange={() => handleCheckboxChange("Top Music")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Music
                </label>
            </div>

            <div style={{ display: 'flex', justifyContent: 'start', backgroundColor: '#EBECED', padding: '40px 40px', gap: '115px', marginBottom: '10px' }}>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Hangouts"] || false}
                        onChange={() => handleCheckboxChange("Top Hangouts")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Hangouts
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Top Shops"] || false}
                        onChange={() => handleCheckboxChange("Top Shops")}
                        style={{ marginRight: "8px" }}
                    />
                    Top Shops
                </label>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <input
                        type="checkbox"
                        checked={preferences["Occupations"] || false}
                        onChange={() => handleCheckboxChange("Occupations")}
                        style={{ marginRight: "8px" }}
                    />
                    Occupations
                </label>
            </div>

            <div style={{ marginBottom: "20px", backgroundColor: '#EBECED', padding: '40px 40px' }}>
                <label style={{ display: "block", marginBottom: "5px" }}>
                    <strong>Please send us feedback and suggestions:</strong>
                </label>
                <textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    style={{ width: "100%", height: "80px", maxWidth: '50%' }}
                />
            </div>

            <button
                type="submit"
                style={{ padding: "10px 20px", backgroundColor: "white", color: "black", border: "1px solid black", cursor: "pointer", borderRadius: '6px' }}
            >
                SUBMIT
            </button>
        </form>
    );
};

export default PerosonalizeView;
