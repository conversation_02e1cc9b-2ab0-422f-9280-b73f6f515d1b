import { UMBRACO_ADRESS } from "../constants/urls";

import useAuth from "./useAuth";

export function useCreateTemporaryFile() {
  const { token } = useAuth();
  async function uploadJsonFile(input: any): Promise<string> {
    const jsonString = JSON.stringify(input);
    console.log(jsonString);
    const blob = new Blob([jsonString], { type: "application/json" });
    const formData = new FormData();
    let tempfileid = crypto.randomUUID();
    formData.append("Id", tempfileid);
    formData.append("File", blob, `payload_${Date.now()}.json`);

    const res = await fetch(
      `${UMBRACO_ADRESS}umbraco/management/api/v1/temporary-file`,
      {
        method: "POST",
        body: formData,
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    if (!res.ok) {
      throw new Error(`Upload failed: ${res.status} ${res.statusText}`);
    }
    return tempfileid;
  }

  async function uploadImageFile(file: File): Promise<string> {
    if (!file || !file.type.startsWith("image/")) {
      throw new Error("Invalid file: expected an image");
    }

    const formData = new FormData();
    const tempfileid = crypto.randomUUID();

    formData.append("Id", tempfileid);
    formData.append("File", file, file.name);

    const res = await fetch(
      `${UMBRACO_ADRESS}umbraco/management/api/v1/temporary-file`,
      {
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!res.ok) {
      throw new Error(`Upload failed: ${res.status} ${res.statusText}`);
    }

    return tempfileid;
  }

  return { uploadJsonFile, uploadImageFile };
}
