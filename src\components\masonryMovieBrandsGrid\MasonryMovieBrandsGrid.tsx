import { Link } from "react-router-dom";
import styles from "./masonryMovieBrandsGrid.module.scss";
import MessageAlert from "../messageAlert/MessageAlert";
import Loader from "../loader/Loader";
import useGetShopPageVideoData from "../../api/useGetShopPageVideoData";
import { UMBRACO_ADRESS } from "../../constants/urls";

interface MasonryMovieBrandsGridProps {
    searchQuery: string;
}

const MasonryMovieBrandsGrid: React.FC<MasonryMovieBrandsGridProps> = ({ searchQuery }) => {
    const { data, isLoading, error } = useGetShopPageVideoData();
    const filteredData = data.filter((shopPage: any) => shopPage.properties.coverPhoto?.[0]?.url);

    const searchFilteredData = filteredData.filter((shopPage: any) => {
        const searchFields: string[] = [];

        Object.values(shopPage.properties || {}).forEach(value => {
            if (typeof value === 'string') {
                searchFields.push(value.toLowerCase());
            }
        });

        if (shopPage.properties.addItem?.items) {
            shopPage.properties.addItem.items.forEach((item: any) => {
                const brand = item.content?.properties?.brand;
                if (brand) {
                    searchFields.push(brand.toLowerCase());
                }
            });
        }

        return searchQuery === '' || searchFields.some(field => field.includes(searchQuery.toLowerCase()));
    });

    return (
        <>
            {isLoading && <Loader />}
            {(!data || data.length === 0) && <MessageAlert type="error" message="No data available" />}
            {error && <MessageAlert type="error" message="Error loading data" />}
            <div className={styles.masonryGrid}>
                {searchFilteredData.map((item: any, index: number) => {
                    const imgSrc = item.properties.coverPhoto?.[0]?.url
                        ? `${UMBRACO_ADRESS}${item.properties.coverPhoto[0].url}`
                        : '';
                    const title = item.name || 'Untitled';
                    return (
                        <Link to={'/clique-video-editor/' + item.id} key={item.id}>
                            <div key={index} className={styles.gridItem}>
                                <img src={imgSrc} alt="media" />
                                <div className={styles.textBlock}>
                                    <h1>{title}</h1>
                                    <p>VIEWS 140</p>
                                </div>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </>
    );
};

export default MasonryMovieBrandsGrid;