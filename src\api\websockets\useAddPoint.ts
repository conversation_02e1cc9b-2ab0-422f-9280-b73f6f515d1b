import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";
import {ISelectionPoint} from "../../types/selectionPoint";

export function useAddPoint() {
  const [isLoadingAddPoint, setIsLoading] = useState(false);
  const [errorAddPoint, setError] = useState<Error | null>(null);

  const sendAddPoint = async (point: ISelectionPoint) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.addPoint,
          {
            "frame_index": point.frameIndex,
            "object_id": point.objectId,
            "label": point.label,
            "coordinates": point.coordinates,
          }
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendAddPoint, isLoadingAddPoint, errorAddPoint };
}
