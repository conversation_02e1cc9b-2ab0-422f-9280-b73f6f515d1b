import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetFrame() {
  const [isLoadingFrame, setIsLoading] = useState(false);
  const [errorFrame, setError] = useState<Error | null>(null);

  const sendGetFrame = async (frameIdx: number) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.getFrame,
          {"frame_idx": frameIdx}
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendGetFrame, isLoadingFrame, errorFrame };
}
