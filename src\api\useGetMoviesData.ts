import { useInfiniteQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS } from '../constants/urls';
import useAuth from './useAuth';

interface FetchMoviesParams {
  pageParam?: number;
  token: string;
}

const fetchMoviesData = async ({
  pageParam = 1,
  token,
}: FetchMoviesParams) => {
  if (!token) {
    throw new Error("No authentication token");
  }

  const response = await fetch(
    `${UMBRACO_ADRESS}umbraco/api/Document/GetAllDocuments?documentType=addShopPageClique&expand=published%2C%20views%2C%20clicks%2C%20mediaUrl%2C%20mediaId&pageNumber=${pageParam}&pageSize=6`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch media data");
  }

  const data = await response.json();
  return data;
};

const useGetMoviesData = () => {
  const { token } = useAuth();

  return useInfiniteQuery({
    queryKey: ["umbracoMediaVideo"],
    queryFn: ({ pageParam = 1 }) =>
      fetchMoviesData({ pageParam, token: token as string }),
    enabled: !!token,
    staleTime: 60000,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    getNextPageParam: (lastPage) => {
      if (lastPage.pageNumber < lastPage.totalPages) {
        return lastPage.pageNumber + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export default useGetMoviesData;
