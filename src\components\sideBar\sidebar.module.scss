@import "../../styles/variables";

.sidebar {
  background-color: $white;
  padding: 20px;
  margin-top: 145px;

  .sidebarItem {
    margin-bottom: 30px;
    min-height: 80px;
    min-width: 300px;
    background-color: $lightGrayBlue;
    border: 1px solid $lightGrayBlue;
    box-shadow: none;
    color: $steelGray;
    padding: 16px;
    transition: 0.2s;

    &:hover {
      background-color: $warmGray;
      border: 1px solid $black;
      transition: 0.5s;
      cursor: pointer;
      color: $black;
    }
  }

  .sidebarItemActive {
    @extend .sidebarItem;
    background-color: $warmGray;
    color: black;
  }

  .sidebarLink {
    text-decoration: none;

    &:last-child .sidebarItem {
      margin-bottom: 0;
    }
  }
}

.sidebarClique {
  margin-top: 0;
}
