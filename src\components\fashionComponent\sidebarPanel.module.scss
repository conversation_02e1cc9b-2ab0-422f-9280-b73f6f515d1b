@import "../../styles/variables";

.sidebar {
  width: 70vw;
  max-width: 1000px;
  margin: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;
  box-sizing: border-box;

  .topSecion {
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h6 {
        font-size: 20px;
        font-weight: 600;
        color: $darkTextColor;
      }
    }

    .formContainer {
      .thumbnailContainer {
        display: flex;
        margin-top: 40px;

        .thumbnailTitle {
          width: 200px;
          color: $darkTextColor;
        }
        .thumbnailErrorContainer {
          display: flex;
          flex-direction: column;
        }

        .thumbnailArea {
          width: 150px;
          height: 150px;
          border: 1px dashed $mistGray;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          margin-bottom: 20px;
          margin-right: 20px;
          transition: all 0.3s ease;

          &:hover {
            border-color: darken($mistGray, 10%);
            box-shadow: 0 4px 8px rgba($black, 0.1);
            background-color: $veryLightPurple;
          }

          &:active {
            transform: scale(0.95);
          }

          &.active {
            border-color: $green;
            background-color: rgba($green, 0.1);
          }

          .icon {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: $darkTextColor;
          }
        }

        .thumbnailError {
          border: 1px solid $errorColor;
        }
      }

      .form {
        margin: 40px 0;

        .textFieldContainer {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          p {
            min-width: 200px;
          }
        }
      }
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;

    button {
      padding: 8px 20px;
      font-size: 16px;
      transition: 0.2s;
      border-radius: 0;
      text-transform: none;

      &:hover {
        filter: brightness(1.2);
      }
    }

    .cancel {
      background: transparent;
      border: none;
      color: $darkTextColor;
      cursor: pointer;
    }

    .submit {
      background: $green;
      color: $white;
      border: none;
      cursor: pointer;
    }
  }
}
.error {
  color: $errorColor;
  font-family: sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  margin-left: 14px;
}
