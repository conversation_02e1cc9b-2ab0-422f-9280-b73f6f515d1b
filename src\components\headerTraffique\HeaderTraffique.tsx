import { Divider } from '@mui/material';
import styles from './headerTraffique.module.scss'
import logo from '../../assets/images/logo-traffique.png'
import the_street_book_logo from '../../assets/images/the-street-book-logo.svg'
import { Link } from 'react-router-dom';
const HeaderTraffique = () => {
    return (
        <div className={styles.header}>
            <div className={styles.container}>
                <Link to={'/'}>
                    <img
                        src={logo}
                        alt="Traffique logo"
                        className={styles.logo}
                    />
                </Link>
                <img
                    src={the_street_book_logo}
                    alt="The street book logo"
                    className={styles.theStreetBookLogo}
                />
            </div>
            <Divider />
        </div>
    );
};
export default HeaderTraffique;