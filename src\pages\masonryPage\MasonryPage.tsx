import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import styles from "./masonryPage.module.scss";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import PhotoGrid from "../../components/photoGrid/PhotoGrid";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import useGetShopPageDataWithFilters from "../../api/useGetShopPageDataWithFilters";

const MasonryPage = () => {
  const { data, isLoading, error } = useGetShopPageDataWithFilters();

  const [searchParams] = useSearchParams();
  const query = searchParams.get("query") || "";

  const [photos, setPhotos] = useState([]);

  useEffect(() => {
    if (data) {
      setPhotos(data);
    }
  }, [data]);

  const searchFilteredData = photos.filter((shopPage: any) => {
    const searchFields = Object.values(shopPage.values || {}).map(
      (value: any) => {
        const v = value?.value;
        return typeof v === "string" ? v.toLowerCase() : "";
      }
    );
    return (
      query === "" ||
      searchFields.some((field) => field.startsWith(query.toLowerCase()))
    );
  });

  if (isLoading) return <Loader />;

  if (!data || data.length === 0)
    return <MessageAlert type="error" message="No data available" />;

  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <PhotoGrid items={searchFilteredData} />
      </div>
    </div>
  );
};

export default MasonryPage;
