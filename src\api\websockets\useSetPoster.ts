import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { getConnectionUrl } from "../../websockets/wsClient";

const sendSetPoster = async (file: File, object_id: number) => {
  const formData = new FormData();
  formData.append('image', file);

  let connectionUrl = getConnectionUrl();
  console.log(`Connection url = ${connectionUrl}`);

  await axios.post(
    `${connectionUrl}/api/object_poster/${object_id}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export default sendSetPoster;