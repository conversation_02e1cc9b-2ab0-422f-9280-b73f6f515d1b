@import "../../styles/variables";

.filters {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
  grid-area: 1 / 2 / 2 / 6;
  margin: 10px 20px;

  a,
  .button {
    appearance: none;
    background: none;
    border: none;
    outline: none;
    font-size: 20px;
    letter-spacing: 1px;
    font-weight: 600;
    text-align: center;
    padding: 12px 40px;
    margin-right: 10px;
    color: $white;
    cursor: pointer;
    transition: 0.3s ease;
    width: auto;

    &:last-of-type {
      margin-right: 0;
    }

    &:hover {
      filter: brightness(1.2);
    }

    &:focus {
      outline: 2px solid lighten($white, 20%);
      outline-offset: 4px;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      filter: none;
    }
  }

  .publish {
    background-color: $green;
    margin-right: 10px;
  }

  .delete {
    background-color: $red;
  }

  .save {
    background-color: $green;
  }
}
