@import '../../styles/variables';

.cliqueVideosPage {
    .container {
        display: grid;
        grid-template-columns: 340px auto auto auto auto;
        grid-template-rows: auto auto auto auto auto;
        grid-column-gap: 0px;
        grid-row-gap: 0px;
        padding: 0 20px;

        .fileUploadContainer {
            grid-area: 2 / 2 / 6 / 6;

            .inputsContainer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 20px 25px 20px;
              
              .inputElement {
                text-align: center;
                width: 100%;
                margin-right: 20px;
          
                &:nth-last-child(1) {
                  margin-right: 0;
                }
          
                .header {
                  background-color: $lightGrayBlue;
                  padding: 15px;
                  color: #7F7978;
                  font-weight: 500;
                  font-size: 20px;
                }
          
                input {
                  width: 100%;
                  margin-top: 20px;
                  padding: 10px;
                  border: 1px solid $lightGrayBlue;
                  text-align: center;
                  font-size: 20px;
                  font-weight: 500;
                }
              }
            }
        }

        .sidebarContainer {
            grid-area: 2 / 1 / 6 / 2;
        }

        .filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            grid-area: 1 / 2 / 2 / 6;
            margin: 10px 20px;
          
            a, .button {
              font-size: 20px;
              letter-spacing: 1px;
              font-weight: 600;
              text-align: center;
              padding: 12px 40px;
              width: calc(25% - 30px);
              margin-right: 10px;
              color: $white;
              cursor: pointer;
              transition: 0.3s ease;
              width: auto;
          
              &:last-of-type {
                margin-right: 0;
              }
          
              &:hover {
                filter: brightness(1.2);
              }
            }

            .videos {
              background-color: $darkGray;
            }

            .save {
              background-color: $green;
              margin-left: 10px;
            }
          }

        .emptySpace {
            grid-area: 1 / 1 / 2 / 2;
        }
    }
}
