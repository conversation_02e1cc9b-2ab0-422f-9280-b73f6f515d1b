import { useState, useEffect, useRef } from "react";
import { UMBRACO_ADRESS } from "../constants/urls";

const AUTH_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/security/back-office/token`;
const CLIENT_ID = "umbraco-back-office-michal";
const CLIENT_SECRET = "test";

const REFRESH_MARGIN = 60000;

const useAuth = () => {
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const requestInProgress = useRef(false);

  useEffect(() => {
    const storedToken = localStorage.getItem("authToken");
    const expiresAt = localStorage.getItem("authTokenExpiresAt");

    if (storedToken && expiresAt && Date.now() < Number(expiresAt)) {
      setToken(storedToken);
      setLoading(false);
    } else {
      fetchToken();
    }

    const intervalId = setInterval(() => {
      const expiresAt = localStorage.getItem("authTokenExpiresAt");
      if (!expiresAt) return;

      if (Date.now() + REFRESH_MARGIN >= Number(expiresAt)) {
        fetchToken();
      }
    }, 30000);

    return () => clearInterval(intervalId);
  }, []);

  const fetchToken = async () => {
    if (requestInProgress.current) return;
    requestInProgress.current = true;
    setLoading(true);
    setError(null);

    try {
      const formData = new URLSearchParams();
      formData.append("grant_type", "client_credentials");
      formData.append("client_id", CLIENT_ID);
      formData.append("client_secret", CLIENT_SECRET);

      const response = await fetch(AUTH_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData.toString(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Authentication error: ${errorData.error_description || response.statusText}`
        );
      }

      const data = await response.json();
      const newToken = data.access_token;
      const expiresIn = data.expires_in || 3600;

      setToken(newToken);
      localStorage.setItem("authToken", newToken);
      localStorage.setItem(
        "authTokenExpiresAt",
        (Date.now() + expiresIn * 1000).toString()
      );
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
      requestInProgress.current = false;
    }
  };

  const authenticatedFetch = async (
    endpoint: string,
    options: RequestInit = {},
    retry: boolean = true
  ) => {
    let storedToken = localStorage.getItem("authToken");
    let expiresAt = localStorage.getItem("authTokenExpiresAt");

    if (!storedToken || !expiresAt || Date.now() >= Number(expiresAt)) {
      await fetchToken();
      storedToken = localStorage.getItem("authToken");
    }

    const response = await fetch(endpoint, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${storedToken}`,
      },
    });

    if (response.status === 401 && retry) {
      await fetchToken();
      storedToken = localStorage.getItem("authToken");

      return fetch(endpoint, {
        ...options,
        headers: {
          ...options.headers,
          Authorization: `Bearer ${storedToken}`,
        },
      });
    }

    return response;
  };
  

  return { token, fetchToken, loading, error, authenticatedFetch };
};

export default useAuth;
