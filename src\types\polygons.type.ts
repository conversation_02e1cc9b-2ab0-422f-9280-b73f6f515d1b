export interface ObjectResult {
  objectId: number;
  polygons: Polygon[];
}

export interface Polygon {
  points: Point[];
}

export interface Point {
  x: number;
  y: number;
}

export interface AddPoint {
  frameIndex: number;
  polygonLists: ObjectResult[];
}

export interface PropagateVideoResponse {
  frameIndex: number;
  results: ObjectResult[];
}

export interface PhotoSelection {
  label: number;
  point: Point;
}

export interface PhotoItem {
  objectId: number;
  polygons: Polygon[];
  selections: PhotoSelection[];
  newSelections: PhotoSelection[];
  thumbnail: File | null;
  thumbnailUrl: string;
  thumbnailKey: string;
  productName: string;
  displayedName: string;
  brand: string;
  link: string;
  zIndex: number;
}

export function createPhotoItem(overrides: Partial<PhotoItem> = {}): PhotoItem {
  return {
    objectId: 0,
    polygons: [],
    selections: [],
    newSelections: [],
    thumbnail: null,
    thumbnailUrl: "",
    thumbnailKey: "",
    productName: "",
    displayedName: "",
    brand: "",
    link: "",
    zIndex: 1,
    ...overrides,
  };
}
