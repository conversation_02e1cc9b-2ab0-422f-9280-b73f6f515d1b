@import '../../styles/_variables.scss';

.movieEditorItemList {
    width: 100%;
    .divider {
        display: flex;
        align-items: center;
        padding: 30px 0 40px 0;

        .dividerTxt {
            width: 30%;
            max-width: 350px;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .inputElement {
                text-align: center;
                margin-right: 20px;
          
                input {
                  padding: 10px;
                  border: 1px solid $lightGrayBlue;
                  text-align: center;
                  font-size: 20px;
                  font-weight: 500;
                }
              }
        }

        .line {
            width: 100%;
            height: 1.5px;
            background-color: $black;
            margin-left: 30px;
        }
    }

    .item {
        display: flex;
        justify-content: space-between;
        position: relative;

        .leftArea {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: $softWhite;
            border: 2px solid $softWhite;
            width: 30%;
            max-width: 350px;
            padding: 20px;
            border-radius: 8px;
            transition: .2s;
            cursor: pointer;

            &:hover {
                border: 2px solid $darkGray;
            }

            .thumbnailArea {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100px;
                width: 100px;
                background-color: $charcoal;
                border-radius: 12px;
                margin-left: 20px;
                border: 1px dashed $mistGray;
                position: relative;
                transition: .2s;
                
                &:hover {
                    border-color: darken($mistGray, 10%);
                    box-shadow: 0 4px 8px rgba($black, 0.1);
                    background-color: $darkGray;
                }

                img {
                    width: 100%; 
                    height: 100%; 
                    object-fit: cover;
                    border-radius: 11px;
                }

                .icon {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    color: $white;
                }
                
                .uploadIcon {
                    position: absolute;
                    color: $white;
                    border: 1px solid $white;
                    border-radius: 50%;
                    opacity: .9;
                }
            }
            
            .buttonContainer {
                div {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 36px;
                    background-color: $paleGray;
                    border-radius: 50%;
                    margin: 10px 10px 10px 0;
                    transition: .2s;
                    border: 2px solid $paleGray;

                    &:hover {
                        background-color: $darkGray;
                    }
                }

                .activeButton {
                    border: 2px solid $darkGray;
                }
            }

            .trashIconWrapper {
                position: absolute;
                top: 10px;
                left: 10px;

                svg {
                    color: $red;
                }
            }
        }

        .activeLeftArea {
            border: 2px solid $darkGray;
        }

        .rightArea {
            width: 100%;
            padding-left: 40px;
        }
    }

    .addButtonContainer {
        width: 100%;
        text-align: center;

        .addButton {
            margin: 20px auto;
        }
    }

    .saveButtonContainer {
        display: flex;
        justify-content: center;
        align-items: center;

        .button {
            font-size: 20px;
            letter-spacing: 1px;
            font-weight: 600;
            text-align: center;
            padding: 12px 40px;
            color: $white;
            cursor: pointer;
            transition: 0.3s ease;
            background-color: $darkGray;
            display: flex;

            &:last-of-type {
                margin-right: 0;
            }

            &:hover {
                filter: brightness(1.2);
            }
        }

        .save {
            background-color: $green;
            width: auto;
        }
    }
}