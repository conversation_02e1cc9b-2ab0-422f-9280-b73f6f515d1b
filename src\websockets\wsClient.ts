import {FUNCTION_APP_BASE_URL, HOST_ADRESS} from "../constants/urls";

type MessageEvent = { event: string; data: any };

let socket: WebSocket;
const listeners: { [key: string]: (data: any) => void } = {};

let dynamicConnectionUrl: string = HOST_ADRESS;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 36;
const INITIAL_RECONNECT_DELAY = 5000; // 5 seconds
let reconnectTimeout: NodeJS.Timeout | null = null;
let userId: string = '';
let onOpenCallbackRef: (() => void) | null = null;
let videoUrlRef: string | undefined = undefined;
let isReconnecting = false;

export const getConnectionUrl = () => dynamicConnectionUrl;

const getReconnectDelay = () => {
  return Math.min(30000, INITIAL_RECONNECT_DELAY * Math.pow(2, reconnectAttempts)); // Max 30 seconds
};

const reconnectWebSocket = () => {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS || !userId || !videoUrlRef) {
    console.error("Maximum reconnection attempts reached or missing connection parameters");
    return;
  }

  isReconnecting = true;
  reconnectAttempts++;
  const delay = getReconnectDelay();

  console.log(`Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}) in ${delay}ms...`);

  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
  }

  reconnectTimeout = setTimeout(() => {
    console.log(`Reconnecting attempt ${reconnectAttempts}...`);
    connectWebSocket(userId, onOpenCallbackRef || (() => {}), videoUrlRef)
      .catch(error => {
        console.error("Reconnection failed:", error);
        reconnectWebSocket();
      });
  }, delay);
};

const resetReconnectionState = () => {
  reconnectAttempts = 0;
  isReconnecting = false;
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
};

export const connectWebSocket = async (userIdParam: string, onOpenCallback: () => void, videoUrl?: string) => {
  userId = userIdParam;
  onOpenCallbackRef = onOpenCallback;
  videoUrlRef = videoUrl;

  if (!videoUrl) {
    console.error("Video URL is required for WebSocket connection");
    return;
  }

  try {
    const urlParts = videoUrl.split('/');
    const contentId = urlParts[urlParts.length - 2]; // Assuming the format is something like "media/contentId/filename"

    const queryParams = new URLSearchParams({
      type: 'video',
      usr: userId || 'demo',
      path: videoUrl
    });

    const functionAppUrl = `${FUNCTION_APP_BASE_URL}/connect/${contentId}?${queryParams.toString()}`;
    console.log("Connecting to function app:", functionAppUrl);

    const response = await fetch(functionAppUrl);
    if (!response.ok) {
      throw new Error(`Failed to connect to function app: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    dynamicConnectionUrl = responseData.connectionUrl || HOST_ADRESS;
    const wsHost = dynamicConnectionUrl;
    socket = new WebSocket(`wss://${wsHost.replace("https://", "")}/ws/${userId}`);

    console.log("WebSocket connection established with host:", wsHost);
  } catch (error) {
    console.error("Error connecting to function app:", error);
    console.warn("Falling back to default WebSocket connection");
    dynamicConnectionUrl = HOST_ADRESS;
    socket = new WebSocket(`wss://${HOST_ADRESS.replace("https://", "")}/ws/${userId}`);
  }

  socket.onmessage = (event) => {
    const message: MessageEvent = JSON.parse(event.data);
    const handler = listeners[message.event];
    if (handler) handler(message.data);
  };

  socket.onclose = (event) => {
    console.warn("WebSocket closed", event);

    if (!event.wasClean && !isReconnecting) {
      console.log("Connection closed unexpectedly. Attempting to reconnect...");
      reconnectWebSocket();
    }
  };

  socket.onerror = (err) => {
    console.error("WebSocket error:", err);
  };

  socket.onopen = () => {
    console.log("WebSocket opened");
    resetReconnectionState();
    onOpenCallback();
  };
};

export const addListener = (event: string, cb: (data: any) => void) => {
  listeners[event] = cb;
};

export const sendMessage = (event: string, data: any) => {
  if (!socket) {
    console.warn("WebSocket is not connected yet. Message not sent:", event, data);
    return;
  }

  const msg = JSON.stringify({ event, data });
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(msg);
  } else {
    socket.addEventListener("open", () => {
      socket.send(msg);
    }, { once: true });
  }
};

export const reconnect = () => {
  if (socket) {
    socket.close();
  }
  reconnectAttempts = 0;
  isReconnecting = false;
  reconnectWebSocket();
};