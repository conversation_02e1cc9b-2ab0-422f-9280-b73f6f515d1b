import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import styles from './homePage.module.scss';
import AddIcon from "@mui/icons-material/Add";
import { Grid, Box, Typography, TextField, TableCell, TableContainer, Table, TableBody, TableRow, TableHead, Paper } from "@mui/material";
import { useRef, useState } from "react";
import Loader from "../../components/loader/Loader";
import useGetHomeData from "../../api/useGetHomeData";
import FileUploadIcon from '@mui/icons-material/FileUpload';
import useUpdateHomeItem from "../../api/useUpdateHomeItem";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DoneIcon from '@mui/icons-material/Done';
import { CLIENTAPP_FRONTEND_ADDRESS } from "../../constants/urls";
import SaveConfirmationModal from '../../components/saveConfirmationModal/SaveConfirmationModal';

const HomePage = () => {
    const { data, isLoading, error } = useGetHomeData();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [fields, setFields] = useState<Record<string, string>>({});
    const [dragActive, setDragActive] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
    const { updateHomeItem } = useUpdateHomeItem();
    if (isLoading) return <Loader />;
    if (error) return <MessageAlert type="error" message="Error loading data" />;

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setSelectedFile(event.target.files[0]);
        }
    };

    const handleThumbnailClick = () => {
        fileInputRef.current?.click();
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setDragActive(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setDragActive(false);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setDragActive(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            setSelectedFile(e.dataTransfer.files[0]);
        }
    };

    const handleFieldChange = (alias: string, value: string) => {
        setFields(prev => ({ ...prev, [alias]: value }));
    };

    const handleSave = async () => {
        const id = '6595b171-21f8-40ad-9f71-5d8aa9ad0339';
        try {
            await updateHomeItem(id, {
                heading: fields.heading || "",
                paragraph: fields.paragraph || "",
                paragraph2: fields.paragraph2 || "",
                imageTitle: fields.imageTitle || "",
                authorName: fields.authorName || "",
                dateLocation: fields.dateLocation || "",
            });
            setIsSaveModalOpen(true);
        } catch (error) {
            console.error("Error updating about us item:", error);
        }
    };

    return (
        <div className={styles.photosPage}>
            <SearchBar />
            <div className={styles.container}>
                <Sidebar />
                <div style={{ width: '100%' }}>
                    <div className={styles.header}>
                        <div>
                            <div className={`${styles.button}`} onClick={() => window.open(CLIENTAPP_FRONTEND_ADDRESS, "_blank")}>
                                <RemoveRedEyeIcon /> <p>Preview</p>
                            </div>
                        </div>
                        <div>
                            <div className={`${styles.button} ${styles.save}`} onClick={handleSave}>
                                <DoneIcon /> <p>Save</p>
                            </div>
                        </div>
                    </div>
                    {data && data.map((item: any) => (
                        <Grid container spacing={2} key={item.id} sx={{ mb: 4 }}>
                            <Grid item xs={12} md={12} pl={12}>
                                <Box component="form" sx={{ mb: 4 }}>
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Name
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.name}
                                    />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        URL Slug
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.route.path}
                                    />
                                    <EditableField label="Heading" alias="heading" markup={item.properties?.heading?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph" alias="paragraph" markup={item.properties?.paragraph?.markup} onChange={handleFieldChange} />
                                    <EditableField label="Paragraph2" alias="paragraph2" markup={item.properties?.paragraph2?.markup} onChange={handleFieldChange} />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Image Title
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.properties?.imageTitle}
                                    />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Date & Location
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.properties?.dateLocation}
                                    />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Photographer Name
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        variant="outlined"
                                        sx={{ mb: 2 }}
                                        defaultValue={item.properties?.authorName}
                                    />
                                    <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>
                                        Photographer Photo
                                    </Typography>

                                    <div className={styles.thumbnailContainer}>
                                        <div
                                            className={`${styles.thumbnailArea} ${dragActive ? styles.active : ""}`}
                                            onClick={handleThumbnailClick}
                                            onDragOver={handleDragOver}
                                            onDragEnter={handleDragEnter}
                                            onDragLeave={handleDragLeave}
                                            onDrop={handleDrop}
                                        >
                                            {selectedFile ? (
                                                <>
                                                    <img
                                                        src={URL.createObjectURL(selectedFile)}
                                                        alt="Thumbnail"
                                                        style={{ width: "100%", height: "100%", objectFit: "cover" }}
                                                    />
                                                    <FileUploadIcon className={styles.uploadIcon} />
                                                </>
                                            ) : item.properties?.authorPhoto && item.properties.authorPhoto.length > 0 ? (
                                                <>
                                                    <img
                                                        src={`https://admintraffique.astroid.com.pl${item.properties.authorPhoto[0].url}`}
                                                        alt="Thumbnail"
                                                        style={{ width: "100%", height: "100%", objectFit: "cover" }}
                                                    />
                                                    <FileUploadIcon className={styles.uploadIcon} />
                                                </>
                                            ) : (
                                                <div className={styles.icon}>
                                                    <AddIcon />
                                                    <p>Choose</p>
                                                </div>
                                            )}

                                            <input
                                                type="file"
                                                ref={fileInputRef}
                                                onChange={handleFileChange}
                                                style={{ display: "none" }}
                                                accept="image/*"
                                            />
                                        </div>
                                    </div>
                                    <div style={{ display: 'flex', gap: '100px' }}>
                                        <TableContainer component={Paper} sx={{
                                            backgroundColor: 'transparent',
                                            maxWidth: '1000px'
                                        }}>
                                            <Table>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>City</TableCell>
                                                        <TableCell>Photo Author</TableCell>
                                                        <TableCell>Photo</TableCell>
                                                        <TableCell>Actions</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {item.properties.photos.items.map((item: any, index: number) => (
                                                        <TableRow
                                                            key={index}
                                                            sx={{
                                                                cursor: 'pointer',
                                                                '&:hover': {
                                                                    backgroundColor: '#fff',
                                                                },
                                                                transition: 'background-color 0.2s ease',
                                                            }}
                                                            onClick={() => console.log(`Row ${index + 1} clicked`)}
                                                        >
                                                            <TableCell>{item.content.properties.pageName}</TableCell>
                                                            <TableCell>{item.content.properties.photoAuthor}</TableCell>
                                                            <TableCell>
                                                                <Box
                                                                    component="img"
                                                                    src={`https://admintraffique.astroid.com.pl${item.content.properties.photo[0].url}`}
                                                                    sx={{
                                                                        height: 240,
                                                                        borderRadius: "4px",
                                                                        objectFit: "cover",
                                                                        mb: 2,
                                                                    }}
                                                                />
                                                            </TableCell>
                                                            <TableCell>-</TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>
                                    </div>
                                </Box>
                            </Grid>
                        </Grid>
                    ))}
                </div>
                <SaveConfirmationModal
                    open={isSaveModalOpen}
                    onClose={() => setIsSaveModalOpen(false)}
                />
            </div>
        </div>
    );
};

const EditableField = ({ label, alias, markup, onChange }: { label: string; alias: string; markup: string | null; onChange: (alias: string, value: string) => void; }) => {
    const [value, setValue] = useState(markup?.replace(/<[^>]*>/g, "") || "");

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
        setValue(e.target.value);
        onChange(alias, e.target.value);
    };

    return (
        <div style={{ marginBottom: "16px" }}>
            <Typography variant="body1" sx={{ mb: 1, color: '#000' }}>{label}</Typography>
            <TextField fullWidth variant="outlined" value={value} onChange={handleChange} />
        </div>
    );
};

export default HomePage;
