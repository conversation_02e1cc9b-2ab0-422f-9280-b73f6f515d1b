import { useState, useEffect, ChangeEvent } from "react";
import { useNavigate } from "react-router-dom";
import FileUpload from "../../components/fileUpload/FileUpload";
import Header from "../../components/shared/Header";
import Sidebar from "../../components/sideBar/SideBar";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import Loader from "../../components/loader/Loader";
import styles from './cliqueVideosPage.module.scss';
import useCreateDocumentWithFile from "../../api/useCreateDocument";
import { formatDate } from "../../middleware/formatDate";
import { useQueryClient } from "@tanstack/react-query";
import { videoDocumentTypeId, videoParentId, videoTemplateId } from "../../constants/urls";

export const CliqueVideosPage = () => {
  const { createDocument, loading, error, success } = useCreateDocumentWithFile();
  const [errorState, setErrorState] = useState<string | null>(null);
  const [mediaUrl, setMediaUrl] = useState<string>("");
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const today = new Date().toISOString().split("T")[0];
  const [formData, setFormData] = useState({
    date: today,
    title: "",
    client: "",
    videographer: "",
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleUploadSuccess = (url: string) => {
    setMediaUrl(url);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSave = () => {
    if (!selectedFile && !mediaUrl) {
      setErrorState("Please select a file!");
      return;
    }

    const formattedDate = formatDate(formData.date, false);

    const documentData = {
      template: { id: videoTemplateId },
      parent: { id: videoParentId },
      documentType: { id: videoDocumentTypeId },
      values: [
        {
          editorAlias: "Umbraco.UploadField",
          alias: "video",
          culture: null,
          segment: null,
          value: mediaUrl,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "date",
          culture: null,
          segment: null,
          value: formattedDate,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "pageTitle",
          culture: null,
          segment: null,
          value: formData.title,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "client",
          culture: null,
          segment: null,
          value: formData.client,
        },
        {
          editorAlias: "Umbraco.TextBox",
          alias: "videographer",
          culture: null,
          segment: null,
          value: formData.videographer,
        },
      ],
      variants: [
        {
          culture: null,
          segment: null,
          state: "Unpublished",
          name: formData.title,
        },
      ],
    };

    createDocument(documentData);
  };

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ["umbracoMediaVideo"] });
        navigate("/clique");
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [success, navigate]);

  return (
    <>
      {errorState && <MessageAlert type="error" message={errorState} />}
      {error && <MessageAlert type="error" message={error} />}
      {loading && <Loader />}
      {success && <MessageAlert type="success" message="Document saved successfully!" />}
      <section className={styles.cliqueVideosPage}>
        <Header />
        <div className={styles.container}>
          <div className={styles.fileUploadContainer}>
            <FileUpload onFileSelect={handleFileSelect} onUploadSuccess={handleUploadSuccess} />
            <div className={styles.inputsContainer}>
              <div className={styles.inputElement}>
                <div className={styles.header}>
                  <p>DATE</p>
                </div>
                <input
                  type="text"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  disabled={true}
                />
              </div>
              <div className={styles.inputElement}>
                <div className={styles.header}>
                  <p>TITLE</p>
                </div>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.inputElement}>
                <div className={styles.header}>
                  <p>CLIENT</p>
                </div>
                <input
                  type="text"
                  name="client"
                  value={formData.client}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.inputElement}>
                <div className={styles.header}>
                  <p>VIDEOGRAPHER</p>
                </div>
                <input
                  type="text"
                  name="videographer"
                  value={formData.videographer}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
          <div className={styles.sidebarContainer}>
            <Sidebar isClique={true} />
          </div>
          <div className={styles.filters}>
            <div></div>
            <div
              className={`${styles.save} ${styles.button} ${loading ? styles.disabled : ""}`}
              onClick={!loading ? handleSave : undefined}
            >
              Save
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
