import React from "react";
import { Dialog, DialogContent, DialogA<PERSON>, Button } from "@mui/material";

interface SaveConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  success?: boolean;
}

const SaveConfirmationModal: React.FC<SaveConfirmationModalProps> = ({
  open,
  onClose,
  success = true,
}) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogContent>
        {success ? (
          <p> Content has been saved succesfully!</p>
        ) : (
          <p> Error saving content!</p>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Ok</Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveConfirmationModal;
