import { useState } from "react";
import { HOST_ADRESS } from "../constants/urls";

interface RemoveObjectResponse {
  success: boolean;
  message: string;
  data?: any;
}

export function useRemoveObjectML() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const removeObject = async (
    sessionId: string | null,
    objectId: number
  ): Promise<RemoveObjectResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `${HOST_ADRESS}/video/remove_object?ObjectId=${objectId}`,
        {
          method: "DELETE",
          headers: {
            "Session-Id": sessionId || "",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      const responseData: RemoveObjectResponse = await response.json();
      return responseData;
    } catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj;
    } finally {
      setIsLoading(false);
    }
  };

  return { removeObject, isLoading, error };
}
