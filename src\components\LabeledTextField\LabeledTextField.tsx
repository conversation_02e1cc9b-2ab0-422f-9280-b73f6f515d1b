import { TextField } from '@mui/material';

interface Props {
    label: string;
    value: string;
    onChange: (value: string) => void;
    onBlur?: () => void;
    sx?: object;
}

const LabeledTextField = ({ label, value, onChange, onBlur, sx }: Props) => (
    <TextField
        fullWidth
        label={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        variant="outlined"
        sx={sx}
    />
);

export default LabeledTextField;