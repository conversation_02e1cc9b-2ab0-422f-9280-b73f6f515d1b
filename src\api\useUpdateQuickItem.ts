import { useState } from "react";
import useAuth from "./useAuth";
import { UMBRACO_ADRESS } from "../constants/urls";

const API_URL = `${UMBRACO_ADRESS}umbraco/management/api/v1/document`;

const useUpdateUpdateQuickItem = () => {
    const { token, fetchToken } = useAuth();
    const [loading, setLoading] = useState(false);
    const [error2, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const updateQuickItem = async (
        id: string,
        data: {
            heading: string;
            paragraph: string;
            paragraph2: string;
            paragraph3: string;
            paragraph4: string;
            paragraph5: string;
            paragraph6: string;
            paragraph7: string;
        }
    ) => {
        setLoading(true);
        setError(null);
        setSuccess(false);

        let currentToken = token;
        if (!currentToken) {
            await fetchToken();
            currentToken = localStorage.getItem("authToken") || "";
            if (!currentToken) {
                setError("Missing authentication token after refresh.");
                setLoading(false);
                return;
            }
        }

        const requestBody = {
            "values": [
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "heading",
                    "value": {
                        "markup": `<h1>${data.heading}</h1>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph",
                    "value": {
                        "markup": `<p><strong>${data.paragraph}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph2",
                    "value": {
                        "markup": `<p><strong>${data.paragraph2}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph3",
                    "value": {
                        "markup": `<p><strong>${data.paragraph3}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph4",
                    "value": {
                        "markup": `<p><strong>${data.paragraph4}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph5",
                    "value": {
                        "markup": `<p><strong>${data.paragraph5}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph6",
                    "value": {
                        "markup": `<p><strong>${data.paragraph6}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
                {
                    "editorAlias": "Umbraco.RichText",
                    "culture": null,
                    "segment": null,
                    "alias": "paragraph7",
                    "value": {
                        "markup": `<p><strong>${data.paragraph7}</strong></p>`,
                        "blocks": {
                            "layout": {},
                            "contentData": [],
                            "settingsData": [],
                            "expose": []
                        }
                    }
                },
            ],
            "variants": [
                {
                    "culture": null,
                    "segment": null,
                    "state": "Published",
                    "name": "Quick Chat - Traffique"
                }
            ],
            "template": {
                "id": "ecc5e8a3-64fd-4999-9697-ee103fbd3c35"
            }
        };

        try {
            let response = await fetch(`${API_URL}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${currentToken}`,
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                if (response.status === 401) {
                    await fetchToken();
                    currentToken = localStorage.getItem("authToken") || "";
                    response = await fetch(`${API_URL}/${id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": `Bearer ${currentToken}`,
                        },
                        body: JSON.stringify(requestBody),
                    });
                }
                if (!response.ok) {
                    throw new Error(`Error: ${response.statusText}`);
                }
            }

            setSuccess(true);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { updateQuickItem, loading, error2, success };
};

export default useUpdateUpdateQuickItem;
