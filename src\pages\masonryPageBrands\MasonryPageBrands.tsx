import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './masonryPageBrands.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';
import MasonryBrandsGrid from '../../components/masonryBrandsGrid/MasonryBrandsGrid';

const MasonryPageBrands: React.FC = () => {
    const { brandName } = useParams<{ brandName: string }>();

    return (
        <div className={styles.photosPage}>
            <HeaderTraffique />
            <div className={styles.contentWrapper}>
                <div className={styles.container}>
                    <MasonryBrandsGrid searchQuery={brandName || ""} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPageBrands;
