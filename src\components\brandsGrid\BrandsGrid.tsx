import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./brandsGrid.module.scss";
import useGetShopPageData from "../../api/useGetShopPageData";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import Loader from "../loader/Loader";
import MessageAlert from "../messageAlert/MessageAlert";

const BrandsGrid = () => {
  const { data, isLoading, error } = useGetShopPageData();
  const [activeSort, setActiveSort] = useState<string>("Title (A > Z)");
  const [visibleCount, setVisibleCount] = useState<number>(9);
  const navigate = useNavigate();

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  if (isLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  // Pobieramy wszystkie marki z danych
  const allBrands: string[] = (data?.flatMap((item: any) =>
    item.properties.addItem?.items?.map((addItem: any) => addItem.content.properties.brand)
  ) ?? []).filter((brand: any): brand is string => typeof brand === "string");

  // Usuwamy duplikaty
  const uniqueBrands: string[] = Array.from(new Set(allBrands));

  // Sortowanie marek
  let sortedBrands = [...uniqueBrands];
  if (activeSort === "Title (A > Z)") {
    sortedBrands.sort((a: string, b: string) => a.localeCompare(b));
  } else if (activeSort === "Title (Z > A)") {
    sortedBrands.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "Newest") {
    // Przykładowe sortowanie – jeżeli masz daty, możesz sortować wg. nich
    sortedBrands.sort((a: string, b: string) => b.localeCompare(a));
  } else if (activeSort === "Oldest") {
    sortedBrands.sort((a: string, b: string) => a.localeCompare(b));
  }

  // Paginacja
  const paginatedBrands = sortedBrands.slice(0, visibleCount);

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 3);
  };

  return (
    <div className={styles.photoGrid}>
      <div className={styles.sortOptions}>
        <p>Sort by:</p>
        {["Title (A > Z)", "Title (Z > A)", "Newest", "Oldest"].map((sortType) => (
          <span
            key={sortType}
            className={`${styles.sortOption} ${activeSort === sortType ? styles.active : ""}`}
            onClick={() => handleSortChange(sortType)}
          >
            {sortType}
          </span>
        ))}
      </div>
      <div className={styles.mainContent}>
        <TableContainer component={Paper} sx={{ backgroundColor: "transparent" }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Brand</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedBrands.map((brand: string, index: number) => (
                <TableRow
                  key={index}
                  sx={{
                    cursor: "pointer",
                    "&:hover": { backgroundColor: "#f0f0f0" },
                    transition: "background-color 0.2s ease",
                  }}
                  onClick={() => navigate(`/brands/${encodeURIComponent(brand)}`)}
                >
                  <TableCell>{brand}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {visibleCount < sortedBrands.length && (
          <div className={styles.loadMore} onClick={handleLoadMore}>
            <p>MORE</p>
            <KeyboardArrowDownIcon />
          </div>
        )}
      </div>
    </div>
  );
};

export default BrandsGrid;
