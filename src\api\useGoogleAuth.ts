import { useState, useEffect } from "react";
import { gapi } from "gapi-script";

const CLIENT_ID = "396447539051-l03at5sok97sgu3j68ct9s8hnrou93rn.apps.googleusercontent.com";
const SCOPES = "https://www.googleapis.com/auth/analytics.readonly";

interface GoogleUser {
    getBasicProfile: () => {
        getName: () => string;
        getEmail: () => string;
        getImageUrl: () => string;
    };
    getAuthResponse: () => { access_token: string };
}

const useGoogleAuth = () => {
    const [user, setUser] = useState<GoogleUser | null>(null);
    const [token, setToken] = useState<string | null>(null);

    useEffect(() => {
        console.log("Inicjalizacja gapi...");
        const initClient = () => {
            gapi.load("client:auth2", () => {
                gapi.client.init({
                    clientId: CLIENT_ID,
                    scope: SCOPES,
                }).then(() => {
                    const authInstance = gapi.auth2.getAuthInstance();
                    const currentUser = authInstance.currentUser.get() as GoogleUser;
                    setUser(currentUser);
                    setToken(currentUser.getAuthResponse().access_token);
                    console.log("Użytkownik:", currentUser);
                    console.log("Token:", currentUser.getAuthResponse().access_token);
                });
            });
        };

        initClient();
    }, []);


    const signIn = () => {
        gapi.auth2.getAuthInstance().signIn().then((user: GoogleUser) => {
            setUser(user);
            setToken(user.getAuthResponse().access_token);
        });
    };

    const signOut = () => {
        gapi.auth2.getAuthInstance().signOut().then(() => {
            setUser(null);
            setToken(null);
        });
    };
    console.log(user)
    console.log(token)
    return { user, token, signIn, signOut };
};

export default useGoogleAuth;