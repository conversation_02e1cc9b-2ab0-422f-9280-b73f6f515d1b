import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './masonryPageProducts.module.scss';
import HeaderTraffique from '../../components/headerTraffique/HeaderTraffique';
import MasonryProductsGrid from '../../components/masonryProductsGrid/MasonryProductsGrid';

const MasonryPageProducts: React.FC = () => {
    const { productName } = useParams<{ productName: string }>();

    return (
        <div className={styles.photosPage}>
            <HeaderTraffique />
            <div className={styles.contentWrapper}>
                <div className={styles.container}>
                    <MasonryProductsGrid searchQuery={productName || ""} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPageProducts;
