import { Link } from "react-router-dom";
import useGetShopPageData from "../../api/useGetShopPageData";
import styles from "./masonryProductsGrid.module.scss";
import MessageAlert from "../messageAlert/MessageAlert";
import Loader from "../loader/Loader";
import { UMBRACO_ADRESS } from "../../constants/urls";

interface MasonryProductsGridProps {
    searchQuery: string;
}

const MasonryProductsGrid: React.FC<MasonryProductsGridProps> = ({ searchQuery }) => {
    const { data, isLoading, error } = useGetShopPageData();
    const selectedCity = 'Everywhere';

    const filteredData = data.filter((shopPage: any) => shopPage.properties.shopPagePhoto?.[0]?.url);

    const cityFilteredData = selectedCity === 'Everywhere'
        ? filteredData
        : filteredData.filter((shopPage: any) => shopPage.properties.location === selectedCity);

    const searchFilteredData = cityFilteredData.filter((shopPage: any) => {
        const searchFields: string[] = [];

        Object.values(shopPage.properties || {}).forEach(value => {
            if (typeof value === 'string') {
                searchFields.push(value.toLowerCase());
            }
        });

        if (shopPage.properties.addItem?.items) {
            shopPage.properties.addItem.items.forEach((item: any) => {
                const productName = item.content?.properties?.productName;
                if (productName) {
                    searchFields.push(productName.toLowerCase());
                }
            });
        }

        return searchQuery === '' || searchFields.some(field => field.includes(searchQuery.toLowerCase()));
    });

    return (
        <>
            {isLoading && <Loader />}
            {(!data || data.length === 0) && <MessageAlert type="error" message="No data available" />}
            {error && <MessageAlert type="error" message="Error loading data" />}
            <div className={styles.masonryGrid}>
                {searchFilteredData.map((item: any, index: number) => {
                    const imgSrc = item.properties.shopPagePhoto?.[0]?.url
                        ? `${UMBRACO_ADRESS}${item.properties.shopPagePhoto[0].url}`
                        : '';
                    const title = item.name || 'Untitled';
                    return (
                        <Link to={'/photos-page/' + item.id} key={item.id}>
                            <div key={index} className={styles.gridItem}>
                                <img src={imgSrc} alt="media" />
                                <div className={styles.textBlock}>
                                    <h1>{title}</h1>
                                    <p>VIEWS 140</p>
                                </div>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </>
    );
};

export default MasonryProductsGrid;