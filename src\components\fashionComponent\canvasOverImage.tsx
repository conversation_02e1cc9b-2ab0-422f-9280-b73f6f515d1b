import styles from "./canvasOverImage.module.scss";
import React, { useEffect, useRef, useState } from "react";
import { PhotoItem, PhotoSelection, Point } from "../../types/polygons.type";
import { maskColors } from "../../constants/colors";
import { addAlphaToHexColor } from "../../middleware/addAlphaToHexColor";

const CanvasOverImage = ({
  imageUrl,
  items,
  onImageClick,
}: {
  imageUrl: string;
  items: PhotoItem[];
  onImageClick: (x: number, y: number) => void;
}) => {
  const backgroundRef = useRef<HTMLCanvasElement>(null);
  const overlayRef = useRef<HTMLCanvasElement>(null);
  const [canvasSize, setCanvasSize] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      setCanvasSize({ width: img.width, height: img.height });
    };
  }, [imageUrl]);

  useEffect(() => {
    const canvas = backgroundRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx || !canvasSize) return;

    const img = new Image();
    img.src = imageUrl;
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
    };
  }, [imageUrl, canvasSize]);

  useEffect(() => {
    if (!items) return;
    const canvas = overlayRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx || !canvasSize) return;
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    items.forEach((item, itemIndex) => {
      item.polygons.forEach((polygon) => {
        const maskColor = maskColors[itemIndex] || "#00FF00";
        drawPolygon(ctx, polygon.points, maskColor);
      });

      item.newSelections.forEach((selection) => {
        drawSelectionPoint(ctx, selection);
      });
    });
  }, [items, canvasSize]);

  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = overlayRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    onImageClick(x, y);
  };

  const drawSelectionPoint = (
    ctx: CanvasRenderingContext2D,
    selection: PhotoSelection
  ) => {
    const radius = 10;
    const x = selection.point.x;
    const y = selection.point.y;
    const label = selection.label;

    // Rysuj szary okrąg
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = "gray";
    ctx.fill();
    ctx.strokeStyle = "black";
    ctx.lineWidth = 2;
    ctx.stroke();

    // Rysuj tekst (+ lub -)
    ctx.fillStyle = "black";
    ctx.font = "16px sans-serif";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    const symbol = label === 1 ? "+" : "-";
    ctx.fillText(symbol, x, y);
  };

  const drawPolygon = (
    ctx: CanvasRenderingContext2D,
    points: { x: number; y: number }[],
    color: string
  ) => {
    if (points.length < 2) return;

    ctx.fillStyle = addAlphaToHexColor(color, 0.5);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }

    ctx.closePath();
    ctx.fill();
    ctx.stroke();
  };

  return (
    <div className={styles.canvasContainer}>
      <canvas
        className={styles.backgroundCanvas}
        ref={backgroundRef}
        width={canvasSize?.width}
        height={canvasSize?.height}
      />
      <canvas
        className={styles.overlayCanvas}
        ref={overlayRef}
        width={canvasSize?.width}
        height={canvasSize?.height}
        onClick={handleCanvasClick}
      />
    </div>
  );
};

export default CanvasOverImage;
