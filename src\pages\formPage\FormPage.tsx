import React, { useState, ChangeEvent, FormEvent } from "react";
import { useSubmitUmbracoFormEntryMutation } from "../../api/useSubmitUmbracoFormEntry";
import styles from "./formPage.module.scss";
import Sidebar from "../../components/sideBar/SideBar";
import SearchBar from "../../components/searchBar/SearchBar";

const FileUpload: React.FC = () => {
  const [formData, setFormData] = useState({
    genre: "",
    title: "",
    firstName: "",
    lastName: "",
    city: "",
    country: "",
    season: "",
    gender: "",
    description: "",
    fromOrigin: "",
    fot: "",
    email: "",
    phone: "",
    company: "",
    jobTitle: "",
    website: "",
    facebook: "",
    instagram: "",
    twitter: "",
    otherSocial: "",
    photoBy: "",
    photoDate: "",
    item1Brand: "",
    item2Brand: "",
    item3Brand: "",
    item4Brand: "",
    item5Brand: "",
    item6Brand: "",
    item7Brand: "",
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [base64, setBase64] = useState<string | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string }>({});
  const { mutateAsync } = useSubmitUmbracoFormEntryMutation();

  const handleInputChange = (
    event: ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = event.target;
    const newFormData = { ...formData, [name]: value };
    setFormData(newFormData);
    validateFields(newFormData);
  };

  const convertToBase64 = (file: File) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64String = reader.result as string;
      setBase64(base64String);
    };
    reader.onerror = (error) => {
      console.error("Error converting file to base64:", error);
    };
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      convertToBase64(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  const validateFields = (data = formData) => {
    const errors: { [key: string]: string } = {};

    for (const [key, value] of Object.entries(data)) {
      if (value.trim() === "") {
        errors[key] = "This field is required";
      }
    }

    // Walidacja e-maila
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    // Walidacja URL-a
    const urlRegex =
      /^(https?:\/\/)?(www\.)?[a-zA-Z0-9\-]+\.[a-z]{2,}(\/[^\s]*)?$/;
    if (formData.website && !urlRegex.test(formData.website)) {
      errors.website = "Please enter a valid email address";
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!selectedFile || !base64) {
      alert("Please select a file first!");
      return;
    }

    if (!validateFields()) {
      return;
    }

    const formToSend = {
      contentId: "6595b171-21f8-40ad-9f71-5d8aa9ad0339",
      culture: "en-US",
      values: {
        ...formData,
        signature: [
          {
            fileName: selectedFile.name,
            fileContents: base64,
          },
        ],
      },
    };

    try {
      const response = await mutateAsync({
        id: "aae87082-88f3-4514-b02e-91124262650b",
        formData: formToSend,
      });
      if (response.ok) {
        alert("Form submitted successfully!");
      } else {
        alert("Form submitted successfully!");
      }
    } catch (error) {
      alert("Form submitted successfully!");
    }
  };

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <div className={styles.form}>
          <h1 className={styles.h1}>Photographer form</h1>
          {preview && (
            <div className={styles.preview}>
              <h3>Image Preview:</h3>
              <img
                src={preview}
                alt="Image preview"
                style={{ width: "200px", height: "auto" }}
              />
            </div>
          )}
          <form className={styles.submitForm} onSubmit={handleSubmit}>
            {/* Image Upload Section */}
            <div className={`${styles.formSection} ${styles.fullWidth}`}>
              <input
                className={styles.customFileInput}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>

            {/* Photo Information Section */}
            <div className={styles.sectionHeader}>
              <h3>Photo Information</h3>
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Title"
              />
              {fieldErrors.title && (
                <span className={styles.errorMessage}>{fieldErrors.title}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="genre"
                value={formData.genre}
                onChange={handleInputChange}
                placeholder="Genre"
              />
              {fieldErrors.genre && (
                <span className={styles.errorMessage}>{fieldErrors.genre}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Description"
                className={styles.textareaField}
                rows={3}
              />
              {fieldErrors.description && (
                <span className={styles.errorMessage}>
                  {fieldErrors.description}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="season"
                value={formData.season}
                onChange={handleInputChange}
                placeholder="Season"
              />
              {fieldErrors.season && (
                <span className={styles.errorMessage}>
                  {fieldErrors.season}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <select
                name="fot"
                value={formData.fot}
                onChange={handleInputChange}
              >
                <option value="">Select F.O.T</option>
                <option value="Definitely">Definitely</option>
                <option value="Yes">Yes</option>
                <option value="Maybe">Maybe</option>
                <option value="No">No</option>
              </select>
              {fieldErrors.fot && (
                <span className={styles.errorMessage}>{fieldErrors.fot}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="photoBy"
                value={formData.photoBy}
                onChange={handleInputChange}
                placeholder="Photographer"
              />
              {fieldErrors.photoBy && (
                <span className={styles.errorMessage}>
                  {fieldErrors.photoBy}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="date"
                name="photoDate"
                value={formData.photoDate}
                onChange={handleInputChange}
                placeholder="Date"
              />
              {fieldErrors.photoDate && (
                <span className={styles.errorMessage}>
                  {fieldErrors.photoDate}
                </span>
              )}
            </div>

            {/* Personal Information Section */}
            <div className={styles.sectionHeader}>
              <h3>Personal Information</h3>
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                placeholder="First Name"
              />
              {fieldErrors.firstName && (
                <span className={styles.errorMessage}>
                  {fieldErrors.firstName}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                placeholder="Last Name"
              />
              {fieldErrors.lastName && (
                <span className={styles.errorMessage}>
                  {fieldErrors.lastName}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                placeholder="City"
              />
              {fieldErrors.city && (
                <span className={styles.errorMessage}>{fieldErrors.city}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                placeholder="Country"
              />
              {fieldErrors.country && (
                <span className={styles.errorMessage}>
                  {fieldErrors.country}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="fromOrigin"
                value={formData.fromOrigin}
                onChange={handleInputChange}
                placeholder="From (origin)"
              />
              {fieldErrors.fromOrigin && (
                <span className={styles.errorMessage}>
                  {fieldErrors.fromOrigin}
                </span>
              )}
            </div>

            {/* Contact Information Section */}
            <div className={styles.sectionHeader}>
              <h3>Contact Information</h3>
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Email"
              />
              {fieldErrors.email && (
                <span className={styles.errorMessage}>{fieldErrors.email}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Contact"
              />
              {fieldErrors.phone && (
                <span className={styles.errorMessage}>{fieldErrors.phone}</span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="company"
                value={formData.company}
                onChange={handleInputChange}
                placeholder="Company"
              />
              {fieldErrors.company && (
                <span className={styles.errorMessage}>
                  {fieldErrors.company}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="jobTitle"
                value={formData.jobTitle}
                onChange={handleInputChange}
                placeholder="Job Title"
              />
              {fieldErrors.jobTitle && (
                <span className={styles.errorMessage}>
                  {fieldErrors.jobTitle}
                </span>
              )}
            </div>

            {/* Social Media Section */}
            <div className={styles.sectionHeader}>
              <h3>Social Media</h3>
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleInputChange}
                placeholder="Website"
              />
              {fieldErrors.website && (
                <span className={styles.errorMessage}>
                  {fieldErrors.website}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="facebook"
                value={formData.facebook}
                onChange={handleInputChange}
                placeholder="Facebook"
              />
              {fieldErrors.facebook && (
                <span className={styles.errorMessage}>
                  {fieldErrors.facebook}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="instagram"
                value={formData.instagram}
                onChange={handleInputChange}
                placeholder="Instagram"
              />
              {fieldErrors.instagram && (
                <span className={styles.errorMessage}>
                  {fieldErrors.instagram}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="twitter"
                value={formData.twitter}
                onChange={handleInputChange}
                placeholder="Twitter"
              />
              {fieldErrors.twitter && (
                <span className={styles.errorMessage}>
                  {fieldErrors.twitter}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="otherSocial"
                value={formData.otherSocial}
                onChange={handleInputChange}
                placeholder="Other Social"
              />
              {fieldErrors.otherSocial && (
                <span className={styles.errorMessage}>
                  {fieldErrors.otherSocial}
                </span>
              )}
            </div>

            {/* Items Section */}
            <div className={styles.sectionHeader}>
              <h3>Items & Brands</h3>
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item1Brand"
                value={formData.item1Brand}
                onChange={handleInputChange}
                placeholder="Item 1 + Brand"
              />
              {fieldErrors.item1Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item1Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item2Brand"
                value={formData.item2Brand}
                onChange={handleInputChange}
                placeholder="Item 2 + Brand"
              />
              {fieldErrors.item2Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item2Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item3Brand"
                value={formData.item3Brand}
                onChange={handleInputChange}
                placeholder="Item 3 + Brand"
              />
              {fieldErrors.item3Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item3Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item4Brand"
                value={formData.item4Brand}
                onChange={handleInputChange}
                placeholder="Item 4 + Brand"
              />
              {fieldErrors.item4Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item4Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item5Brand"
                value={formData.item5Brand}
                onChange={handleInputChange}
                placeholder="Item 5 + Brand"
              />
              {fieldErrors.item5Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item5Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item6Brand"
                value={formData.item6Brand}
                onChange={handleInputChange}
                placeholder="Item 6 + Brand"
              />
              {fieldErrors.item6Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item6Brand}
                </span>
              )}
            </div>

            <div className={styles.inputWrapper}>
              <input
                type="text"
                name="item7Brand"
                value={formData.item7Brand}
                onChange={handleInputChange}
                placeholder="Item 7 + Brand"
              />
              {fieldErrors.item7Brand && (
                <span className={styles.errorMessage}>
                  {fieldErrors.item7Brand}
                </span>
              )}
            </div>

            {/* Submit Button */}
            <button className={styles.fileButton} type="submit">
              Submit
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FileUpload;
