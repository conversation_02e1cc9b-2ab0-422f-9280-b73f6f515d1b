export const getVideoDimensions = async (url: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      video.src = url;
  
      video.addEventListener("loadedmetadata", () => {
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
        });
      });
  
      video.addEventListener("error", () => {
        reject(new Error("Failed to load video metadata"));
      });
    });
  };
  