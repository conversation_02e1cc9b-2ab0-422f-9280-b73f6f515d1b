import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";

export function useGetAllPoints() {
    const [isLoadingGetAllPoints, setIsLoading] = useState(false);
    const [errorGetAllPoints, setError] = useState<Error | null>(null);

    const sendGetAllPoints = async () => {
        setIsLoading(true);
        setError(null);

        try {
            sendMessage(
                wsMessages.getAllPoints,
                null
            )
        }
        catch (err) {
            const errorObj = err as Error;
            setError(errorObj);
            throw errorObj;
        } finally {
            setIsLoading(false);
        }
    };

    return { sendGetAllPoints, isLoadingGetAllPoints, errorGetAllPoints };
}
