import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    XAxis,
    <PERSON>A<PERSON><PERSON>,
    Tooltip,
    CartesianGrid,
    Cell,
} from "recharts";


const styles = [
    "Hipster", "Fashionista", "Rapper/streetwear", "Casual", "Artsy",
    "Androgyny", "Bohemian", "HipHop", "Minimalist", "Chic"
];


const generateRandomData = () => {
    return styles.map((name) => ({
        name,
        count: Math.floor(Math.random() * 4000) + 500,
    }));
};

const colors = [
    "#84C9A2", // Hipster
    "#C7CA77", // Fashionista
    "#676BC6", // Rapper/streetwear
    "#C3896C", // Casual
    "#76ABC8", // Artsy
    "#8FC875", // Androgyny
    "#AE6FC7", // Bohemian
    "#C070BE", // HipHop
    "#85C87B", // Minimalist
    "#C6A370", // Chic
];

const StyleBarChart: React.FC = () => {
    const data = generateRandomData();
    return (
        <div style={{ display: "flex", justifyContent: "center" }}>
            <div style={{ textAlign: "left", borderLeft: "1px solid grey", paddingLeft: "20px" }}>
                <h2 style={{ color: "black", fontWeight: 'normal', fontSize: "20px", marginBottom: "30px" }}>Most popular styles</h2>
                <div style={{ display: "flex", justifyContent: "left", flexWrap: "wrap", maxWidth: "600px", color: "black" }}>
                    {data.map((item, index) => (
                        <div
                            key={item.name}
                            style={{
                                display: "flex",
                                alignItems: "center",
                                margin: "5px 10px",
                                fontSize: "14px",
                                marginLeft: "30px"
                            }}
                        >
                            <span
                                style={{
                                    width: "12px",
                                    height: "12px",
                                    backgroundColor: colors[index],
                                    display: "inline-block",
                                    marginRight: "8px",
                                    borderRadius: "50px"
                                }}
                            ></span>
                            {item.name}
                        </div>
                    ))}
                </div>
                <BarChart
                    width={600}
                    height={400}
                    data={data}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    barGap={0}
                    barCategoryGap="0%"
                >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="none" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8">
                        {data.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={colors[index]} />
                        ))}
                    </Bar>
                </BarChart>
                <p style={{ textAlign: "center", color: "black" }}>all</p>
            </div>
        </div>
    );
};

export default StyleBarChart;
