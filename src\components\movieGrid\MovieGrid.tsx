import styles from "./movieGrid.module.scss";
import { <PERSON> } from "react-router-dom";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import useGetMoviesData from "../../api/useGetMoviesData";
import movieImg from "../../assets/images/movie-img.png";
import Loader from "../loader/Loader";
import MessageAlert from "../messageAlert/MessageAlert";
import { UMBRACO_ADRESS } from "../../constants/urls";

const MovieGrid = () => {
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetMoviesData();

  if (error)
    return (
      <MessageAlert type="error" message="Error loading data" />
    );

  const movieData =
    data?.pages.flatMap((page) => page.data) || [];

  return (
    <div className={styles.movieGridContainer}>
      <div className={styles.gridContainer}>
        {movieData.length > 0 ? (
          movieData.map((item) => {
            const coverPhotoUrl = item.values.find(
              (val: any) => val.editorAlias === "coverPhoto"
            )?.value?.mediaUrl;
            const fullCoverPhotoUrl = coverPhotoUrl
            ? `${UMBRACO_ADRESS}${coverPhotoUrl}`
            : null;
            const title = item.values.find(
              (val: any) => val.editorAlias === "pageTitle"
            )?.value;

            return (
              <Link
                to={`/clique-video-editor/${item.id}`}
                state={{ item }}
                key={item.id}
              >
                <div className={styles.movieCard}>
                  <div className={styles.movieImageContainer}>
                    {fullCoverPhotoUrl ? (
                      <img
                        src={fullCoverPhotoUrl}
                        alt="Photo"
                        className={styles.movieImage}
                      />
                    ) : (
                      <img
                        src={movieImg}
                        alt="Photo"
                        className={styles.movieImage}
                      />
                    )}
                    <div className={`${styles.label} ${
                      item.published
                        ? styles.published
                        : styles.unpublished
                    }`}>
                      <p>
                        {item.published
                          ? "Published"
                          : "Unpublished"}
                      </p>
                    </div>
                  </div>
                  <div
                    className={`${styles.status} ${
                      item.published
                        ? styles.published
                        : styles.unpublished
                    }`}
                  ></div>
                  <div className={styles.movieDetails}>
                    <h3>{title}</h3>
                    <p>Views: {item.views}</p>
                    <p>Clicks: {item.clicks}</p>
                  </div>
                </div>
              </Link>
            );
          })
        ) : (
          <Loader />
        )}
      </div>
      {hasNextPage && (
        <div
          className={styles.loadMore}
          onClick={() => fetchNextPage()}
          style={{ cursor: "pointer" }}
        >
          <p>
            {isFetchingNextPage ? "Loading..." : "MORE"}
          </p>
          <KeyboardArrowDownIcon />
        </div>
      )}
    </div>
  );
};

export default MovieGrid;
