import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./cliqueSearchBar.module.scss";
import SearchIcon from "@mui/icons-material/Search";

const CliqueSearchBar = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  const handleSearch = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && searchQuery.trim() !== "") {
      navigate(`/masonryMovie?query=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className={styles.searchBar}>
      <SearchIcon className={styles.icon} fontSize="small" />
      <input
        className={styles.input}
        type="text"
        placeholder="Type anywhere to search"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        onKeyDown={handleSearch}
      />
    </div>
  );
};

export default CliqueSearchBar;
