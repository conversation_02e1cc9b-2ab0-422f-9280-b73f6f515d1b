import { useState } from "react";
import {sendMessage} from "../../websockets/wsClient"
import {wsMessages} from "../../websockets/wsMessages";
import {FormItem} from "../../types/formItem";

export function useUpdateObject() {
  const [isLoadingUpdateObject, setIsLoading] = useState(false);
  const [errorUpdateObject, setError] = useState<Error | null>(null);

  const sendUpdateObject = async (object: FormItem) => {
    setIsLoading(true);
    setError(null);

    try {
      sendMessage(
          wsMessages.updateObject,
          {
            "object_id": object.id,
            "name": object.itemName,
            "url": object.link,
            "brand": object.brand,
            "id_name": object.identificationName,
            "image": object.image,
          }
      )
    }
    catch (err) {
      const errorObj = err as Error;
      setError(errorObj);
      throw errorObj; 
    } finally {
      setIsLoading(false);
    }
  };

  return { sendUpdateObject, isLoadingUpdateObject, errorUpdateObject };
}
