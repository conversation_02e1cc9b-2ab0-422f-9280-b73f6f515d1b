import { useQuery } from '@tanstack/react-query';
import { UMBRACO_ADRESS_PHOTOGRAPHER } from '../constants/urls';

const fetchQuickChat = async () => {
  const response = await fetch(
    `${UMBRACO_ADRESS_PHOTOGRAPHER}umbraco/delivery/api/v2/content?filter=contentType:quick&take=1000000`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch shop page data');
  }

  const data = await response.json();

  return data.items;
};

const useGetQuickChatData = () => {
  return useQuery({
    queryKey: ['quick'],
    queryFn: fetchQuickChat,
  });
};

export default useGetQuickChatData;
