import React from 'react';
import { useParams } from 'react-router-dom';
import styles from './masonryPageMovieBrands.module.scss';
import Header from '../../components/shared/Header';
import MasonryMovieBrandsGrid from '../../components/masonryMovieBrandsGrid/MasonryMovieBrandsGrid';

const MasonryPageMovieBrands: React.FC = () => {
    const { brandName } = useParams<{ brandName: string }>();

    return (
        <div className={styles.photosPage}>
            <Header />
            <div className={styles.contentWrapper}>
                <div className={styles.container}>
                    <MasonryMovieBrandsGrid searchQuery={brandName || ""} />
                </div>
            </div>
        </div>
    );
};

export default MasonryPageMovieBrands;
